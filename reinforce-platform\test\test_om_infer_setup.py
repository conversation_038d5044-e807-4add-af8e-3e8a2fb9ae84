# -*- coding: utf-8 -*-
import cv2
import numpy as np
from ais_bench.infer.interface import InferSession

# 类别定义
CLASSES = {
    0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle', 4: 'airplane', 5: 'bus', 6: 'train', 7: 'truck',
    8: 'boat', 9: 'traffic light', 10: 'fire hydrant', 11: 'stop sign', 12: 'parking meter', 13: 'bench',
    14: 'bird', 15: 'cat', 16: 'dog', 17: 'horse', 18: 'sheep', 19: 'cow', 20: 'elephant', 21: 'bear',
    22: 'zebra', 23: 'giraffe', 24: 'backpack', 25: 'umbrella', 26: 'handbag', 27: 'tie', 28: 'suitcase',
    29: 'frisbee', 30: 'skis', 31: 'snowboard', 32: 'sports ball', 33: 'kite', 34: 'baseball bat',
    35: 'baseball glove', 36: 'skateboard', 37: 'surfboard', 38: 'tennis racket', 39: 'bottle',
    40: 'wine glass', 41: 'cup', 42: 'fork', 43: 'knife', 44: 'spoon', 45: 'bowl', 46: 'banana', 47: 'apple',
    48: 'sandwich', 49: 'orange', 50: 'broccoli', 51: 'carrot', 52: 'hot dog', 53: 'pizza', 54: 'donut',
    55: 'cake', 56: 'chair', 57: 'couch', 58: 'potted plant', 59: 'bed', 60: 'dining table', 61: 'toilet',
    62: 'tv', 63: 'laptop', 64: 'mouse', 65: 'remote', 66: 'keyboard', 67: 'cell phone', 68: 'microwave',
    69: 'oven', 70: 'toaster', 71: 'sink', 72: 'refrigerator', 73: 'book', 74: 'clock', 75: 'vase',
    76: 'scissors', 77: 'teddy bear', 78: 'hair drier', 79: 'toothbrush'
}

# 置信度阈值
CONFIDENCE = 0.4
# NMS 的 IoU 阈值
IOU = 0.45

# 为每个类别分配随机颜色
colors = np.random.uniform(0, 255, size=(len(CLASSES), 3))


def draw_bounding_box(img, class_id, confidence, x, y, x_plus_w, y_plus_h):
    """
    在图像上绘制边界框和类别标签

    参数：
        img - 原始图像
        class_id - 类别ID
        confidence - 置信度
        x, y - 左上角坐标
        x_plus_w, y_plus_h - 右下角坐标
    """
    label = "{} {:.2f}".format(CLASSES[class_id], confidence)
    color = colors[class_id]

    # 画框
    cv2.rectangle(img, (x, y), (x_plus_w, y_plus_h), color, 2)

    # 获取文本大小
    label_size, _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)
    label_width, label_height = label_size

    label_x = x
    label_y = y - 10 if y - 10 > label_height else y + 10

    # 背景框
    cv2.rectangle(img, (label_x, label_y - label_height),
                  (label_x + label_width, label_y + label_height), color, cv2.FILLED)
    # 文字
    cv2.putText(img, label, (label_x, label_y), cv2.FONT_HERSHEY_SIMPLEX,
                0.5, (0, 0, 0), 1, cv2.LINE_AA)


def main(session, original_image):
    """
    加载模型，执行推理，绘制检测框并保存结果图像

    参数：
        session - 模型
        original_image - 图片值

    返回：
        original_image - 画框的图片
        detections - 包含每个目标信息的列表
    """

    height, width, _ = original_image.shape

    # 变为正方形图像用于推理
    length = max(height, width)
    image = np.zeros((length, length, 3), np.uint8)
    image[0:height, 0:width] = original_image

    # 缩放因子
    scale = length / 640

    # 预处理图像
    blob = cv2.dnn.blobFromImage(image, scalefactor=1.0 / 255, size=(640, 640), swapRB=True)

    # 模型推理
    outputs = session.infer(feeds=[blob], mode="static")

    # 打印原始输出信息用于调试
    print(f"原始输出形状: {[out.shape for out in outputs]}")
    print(f"原始输出类型: {[type(out) for out in outputs]}")

    # 转换输出维度：从 (1, 84, 8400) -> (8400, 84)
    outputs = np.array([cv2.transpose(outputs[0][0])])
    rows = outputs.shape[1]

    print(f"转换后输出形状: {outputs.shape}")
    print(f"行数: {rows}")
    print(f"每行数据长度: {outputs.shape[2] if len(outputs.shape) > 2 else 'N/A'}")

    boxes = []
    scores = []
    class_ids = []

    # 解析输出
    for i in range(rows):
        classes_scores = outputs[0][i][4:]

        # 检查数组是否为空或无效
        if classes_scores.size == 0:
            print(f"Row {i}: classes_scores is empty")
            continue

        # 确保数据类型正确
        classes_scores = np.ascontiguousarray(classes_scores, dtype=np.float32)

        # 检查数组是否包含有效数据
        if np.all(np.isnan(classes_scores)) or np.all(np.isinf(classes_scores)):
            print(f"Row {i}: classes_scores contains invalid data")
            continue

        # 使用numpy方法替代cv2.minMaxLoc，更加稳定
        maxClassIndex = np.argmax(classes_scores)
        maxScore = classes_scores[maxClassIndex]

        # 调试信息
        if i < 5:  # 只打印前5行的调试信息
            print(f"Row {i}: maxScore={maxScore:.4f}, maxClassIndex={maxClassIndex}")

        if maxScore >= CONFIDENCE:
            box = [
                (outputs[0][i][0] - outputs[0][i][2] / 2) * scale,  # x 左上角
                (outputs[0][i][1] - outputs[0][i][3] / 2) * scale,  # y 左上角
                outputs[0][i][2] * scale,  # 宽
                outputs[0][i][3] * scale   # 高
            ]
            boxes.append(box)
            scores.append(maxScore)
            class_ids.append(maxClassIndex)

    # 非极大值抑制
    result_boxes = cv2.dnn.NMSBoxes(boxes, scores, CONFIDENCE, IOU, 0.5)

    detections = []

    # 绘制边界框
    for i in range(len(result_boxes)):
        index = result_boxes[i]
        box = boxes[index]
        detection = {
            "class_id": class_ids[index],
            "class_name": CLASSES[class_ids[index]],
            "confidence": scores[index],
            "box": box,
            "scale": scale,
        }
        detections.append(detection)
        draw_bounding_box(
            original_image,
            class_ids[index],
            scores[index],
            round(box[0]),
            round(box[1]),
            round(box[0] + box[2]),
            round(box[1] + box[3])
        )

    return original_image, detections

if __name__ == "__main__":
    model_path = "best_om.om"
    input_image_path = "bus.jpg"

    try:
        # 检查文件是否存在
        import os
        if not os.path.exists(model_path):
            print(f"错误: 模型文件不存在: {model_path}")
            exit(1)

        if not os.path.exists(input_image_path):
            print(f"错误: 图片文件不存在: {input_image_path}")
            exit(1)

        # 创建推理会话
        print(f"加载模型: {model_path}")
        session = InferSession(device_id=0, model_path=model_path)
        print("模型加载成功")

        # 图片推理
        print(f"读取图片: {input_image_path}")
        image = cv2.imread(input_image_path)
        if image is None:
            print(f"错误: 无法读取图片: {input_image_path}")
            exit(1)

        print(f"图片尺寸: {image.shape}")

        print("开始推理...")
        draw_image, detections = main(session, image)

        print(f"推理完成，检测到 {len(detections)} 个目标")
        for i, det in enumerate(detections):
            print(f"目标 {i+1}: {det['class_name']} (置信度: {det['confidence']:.2f})")

        output_path = "output_image.jpg"
        cv2.imwrite(output_path, draw_image)
        print(f"结果已保存到: {output_path}")

    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()