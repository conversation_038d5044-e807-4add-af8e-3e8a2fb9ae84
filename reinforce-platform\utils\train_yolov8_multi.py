#!/usr/bin/env python3
"""
华为NPU单机多卡YOLO训练脚本
支持单机多/单卡训练
"""

import os
import sys
import time
import json
import logging
import psutil
import argparse
import subprocess
import traceback
import threading
import copy
from pathlib import Path
import torch
from datetime import datetime


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def detect_device():
    """
    检测可用的训练设备，优先使用NPU
    返回: 设备标识符 ('cpu', 'cuda:0', 或 'npu:0,1,2,3')
    """
    try:
        # 首先检查NPU
        try:
            import torch_npu
            if torch_npu.npu.is_available():
                npu_count = torch_npu.npu.device_count()
                logger.info(f"发现 {npu_count} 个可用的NPU设备")

                # 根据NPU数量返回设备配置
                if npu_count > 1:
                    device = f"npu:{','.join(map(str, range(npu_count)))}"
                    print(f"🔄 自动配置: 使用所有{npu_count}个NPU - {device}")
                    return device
                else:
                    print("🔄 自动配置: 使用单个NPU - npu:0")
                    return 'npu:0'
        except ImportError:
            logger.warning("未安装torch_npu，跳过NPU检测")
            pass
        except Exception as e:
            logger.warning(f"NPU检测失败: {e}")
            pass

        # 检查GPU
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            if device_count > 0:
                gpu_name = torch.cuda.get_device_name(0)
                logger.info(f"使用GPU: {gpu_name}")
                if device_count > 1:
                    return f"cuda:{','.join(map(str, range(device_count)))}"
                else:
                    return 'cuda:0'
        logger.info("使用CPU进行训练")
        return 'cpu'
    except Exception as e:
        logger.warning(f"设备检测出错: {e}")
        return 'cpu'

def get_resource_usage():
    """
    获取系统资源使用情况
    返回: 包含CPU、内存、GPU和NPU使用率的字典
    """
    usage = {
        'cpu_usage': psutil.cpu_percent(),
        'memory_usage': psutil.virtual_memory().percent,
        'gpu_usage': 0.0,
        'npu_usage': 0.0,
        'timestamp': time.time()
    }

    # 获取GPU使用率
    try:
        if torch.cuda.is_available():
            usage['gpu_usage'] = float(torch.cuda.memory_allocated() / torch.cuda.max_memory_allocated() * 100)
    except:
        pass

    # 获取NPU使用率
    try:
        result = subprocess.check_output(['npu-smi', 'info'], universal_newlines=True)
        for line in result.split('\n'):
            if '910' in line and 'OK' in line:
                parts = line.split('|')
                if len(parts) >= 4:
                    power_info = parts[3].strip()
                    if 'W' in power_info:
                        power = float(power_info.split('W')[0].strip())
                        usage['npu_usage'] = (power / 250.0) * 100  # 假设最大功率250W
    except:
        pass

    return usage

def parse_training_config(config, dataset_path=None, device=None, task_id=None):
    """
    解析训练配置，转换为YOLOv8可接受的参数格式
    """
    print("config", config)
    # 基础训练参数
    train_args = {
        'epochs': int(config['parameters']['epochs']), # 训练轮数
        'device': device or detect_device(), # 训练设备
        'project': 'runs_detect', # 训练结果保存路径
        'name': task_id or config.get("id", "npu_train"), # 训练结果保存名称，优先使用传入的task_id
        'exist_ok': True, # 是否覆盖训练结果
        'plots': False,  # 禁用绘图以提高性能
    }
    # 模型保存频率配置
    train_args['save_period'] = int(config['otherParams'].get('checkpointFreq', 1))

    # 数据集路径 - 优先使用命令行参数提供的路径
    if dataset_path:
        train_args['data'] = dataset_path
    else:
        # 数据集和验证参数
        dataset_dir = os.path.join('datasets', config['training']['dataset']['name']) # 数据集路径
        train_args['data'] = os.path.join(dataset_dir, 'data.yaml') # 数据集路径

    # 批次大小和学习率
    train_args['batch'] = int(config['parameters'].get('batchSize', 128)) # 批次大小
    train_args['lr0'] = float(config['parameters'].get('learningRate', 0.01)) # 学习率

    # 图像尺寸
    train_args['imgsz'] = int(config['parameters'].get('imageSize', 640)) # 图像尺寸

    # 优化器相关参数
    optimizer = config['otherParams'].get('optimizer', 'SGD').lower() # 优化器
    train_args['optimizer'] = optimizer # 优化器

    if optimizer in ['sgd', 'adam', 'adamw']:
        train_args['momentum'] = float(config['otherParams'].get('momentum', 0.937)) # 动量
        train_args['weight_decay'] = float(config['otherParams'].get('weightDecay', 0.0005)) # 权重衰减

    # 早停和检查点
    if 'earlyStopping' in config['otherParams']:
        train_args['patience'] = int(config['otherParams']['earlyStopping'])

    if 'checkpointFreq' in config['otherParams']:
        train_args['save_period'] = int(config['otherParams']['checkpointFreq'])

    # 混合精度训练
    if config['otherParams'].get('useMixedPrecision', False):
        train_args['amp'] = True  # 自动混合精度

    # 学习率策略
    lr_strategy = config['parameters'].get('learningRateStrategy', '').lower()
    if lr_strategy == '余弦衰减':
        train_args['cos_lr'] = True  # 使用余弦学习率调度

    # 标签平滑
    if 'labelSmoothing' in config['otherParams']:
        train_args['label_smoothing'] = float(config['otherParams']['labelSmoothing'])

    # Dropout
    if 'dropout' in config['otherParams']:
        train_args['dropout'] = float(config['otherParams']['dropout'])

    # 预热步数
    if 'warmupSteps' in config['otherParams']:
        warmup_epochs = int(config['otherParams']['warmupSteps']) // (train_args['batch'] * 100)  # 估算预热轮数
        train_args['warmup_epochs'] = max(1, warmup_epochs)  # 至少1轮
        train_args['warmup_momentum'] = 0.8
        train_args['warmup_bias_lr'] = 0.1

    # 数据加载器配置
    train_args['workers'] = int(config.get('resources', {}).get('workers', 8))

    # 缓存配置
    if config['otherParams'].get('checkpointFreq', 1):
        train_args['cache'] = True


    return train_args

def setup_npu_environment():
    """检查NPU训练环境"""
    try:
        import torch_npu
        print(f"✅ torch_npu版本: {torch_npu.__version__}")

        # 检查NPU可用性
        if not torch_npu.npu.is_available():
            raise RuntimeError("NPU不可用，请检查驱动和torch_npu安装")

        npu_count = torch_npu.npu.device_count()
        print(f"✅ 检测到 {npu_count} 个NPU设备")

        # 显示NPU信息
        for i in range(npu_count):
            try:
                device_name = torch_npu.npu.get_device_name(i)
                device_props = torch_npu.npu.get_device_properties(i)
                memory_gb = device_props.total_memory / 1024**3
                print(f"   NPU:{i} - {device_name} ({memory_gb:.1f}GB)")
            except Exception as e:
                print(f"   NPU:{i} - 设备信息获取失败: {e}")

        return npu_count

    except ImportError:
        print("⚠️  torch_npu未安装，将使用其他可用设备")
        return 0
    except Exception as e:
        print(f"⚠️  NPU环境检查失败: {e}")
        return 0

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='华为NPU多机多卡YOLO训练')

    # 配置文件参数
    parser.add_argument('--config', type=str, default='training_config.json', help='训练配置文件路径')
    parser.add_argument('--task_id', type=str, help='任务ID，用于生成输出目录名')

    # 基本训练参数 (可覆盖配置文件)
    parser.add_argument('--model', type=str, help='模型文件路径，优先级高于配置文件')
    parser.add_argument('--data', type=str, help='数据集配置文件，优先级高于配置文件')

    # 环境路径参数
    parser.add_argument('--mount_path', type=str, help='ultralytics库完整路径（ULTRALYTICS_DIR）')

    # 是否续训
    parser.add_argument('--resume', type=str, help='是否从上次训练中断处继续训练')

    return parser.parse_args()

def single_machine_train(train_args, model_path, device, resume_training=False):
    """单机训练（单卡或多卡）"""
    # ultralytics已经在main函数中导入，这里直接使用
    from ultralytics import YOLO

    print("=" * 60)
    print("🚀 启动单机NPU训练")
    print(f"📱 设备: {device}")
    print(f"📊 批次大小: {train_args['batch']}")
    print(f"🔄 训练轮次: {train_args['epochs']}")
    print(f"🔄 续训模式: {'是' if resume_training else '否'}")
    print("=" * 60)

    # 创建模型
    model = YOLO(model_path)

    # 获取任务名称
    task_name = train_args.get('name', 'task_default')

    # 存储每个epoch的训练指标，用于后续合并模型信息
    epoch_metrics_cache = {}

    # 添加训练回调函数
    def on_train_epoch_end(trainer):
        """每个训练周期结束时的回调函数"""
        nonlocal task_name, epoch_metrics_cache  # 使用闭包访问外部变量

        # 获取资源使用情况
        resource_metrics = get_resource_usage()

        # 安全获取metrics，如果为None则使用空字典
        metrics = trainer.metrics if trainer.metrics is not None else {}

        # 从trainer.tloss获取训练损失组件
        if hasattr(trainer, 'tloss') and len(trainer.tloss) >= 3:
            # 将tensor转换为Python的float值
            box_loss = round(float(trainer.tloss[0]), 4)
            cls_loss = round(float(trainer.tloss[1]), 4)
            dfl_loss = round(float(trainer.tloss[2]), 4)
        else:
            # 如果tloss不可用，使用默认值
            box_loss = 0.0
            cls_loss = 0.0
            dfl_loss = 0.0

        # 合并训练指标和资源使用指标
        combined_metrics = {
            'epoch': trainer.epoch,
            'train/box_loss': box_loss,
            'train/cls_loss': cls_loss,
            'train/dfl_loss': dfl_loss,
            'metrics/precision': metrics.get('metrics/precision(B)', 0.0),
            'metrics/recall': metrics.get('metrics/recall(B)', 0.0),
            'metrics/mAP50': metrics.get('metrics/mAP50(B)', 0.0),
            'metrics/mAP50-95': metrics.get('metrics/mAP50-95(B)', 0.0),
            **resource_metrics
        }

        # 记录日志
        logger.info(f"Epoch {trainer.epoch} metrics: {combined_metrics}")

        # 保存指标到文件
        try:
            with open("/workspace/training_metrics.json", 'a') as f:
                f.write(json.dumps(combined_metrics) + '\n')
        except Exception as e:
            logger.error(f"保存训练指标失败: {e}")

        # 缓存当前epoch的指标，保存原始metrics数据用于后续模型信息合并
        epoch_metrics_cache[trainer.epoch] = {
            # 保存原始metrics数据
            **metrics,
            # 添加额外信息
            'epoch': trainer.epoch,
            'timestamp': datetime.now().isoformat()
        }

        logger.info(f"已缓存Epoch {trainer.epoch}的指标数据")

    # 注册回调函数
    model.add_callback('on_train_epoch_end', on_train_epoch_end)

    # 更新设备配置
    train_args['device'] = device
    train_args['verbose'] = True
    train_args['save'] = True
    train_args['plots'] = True
    train_args['val'] = True
    # train_args['save_period'] = 1  # 每轮保存一次

    # 设置续训参数
    if resume_training:
        train_args['resume'] = True
        logger.info("启用续训模式")
    else:
        train_args['resume'] = False
        logger.info("启用全新训练模式")

    print("🎯 开始训练...")
    try:
        results = model.train(**train_args)
        print("✅ 训练完成！")
        print(f"📈 结果保存在: {model.trainer.save_dir}")

        # 训练完成后，收集所有模型信息并保存
        try:
            save_dir = model.trainer.save_dir
            weights_dir = os.path.join(save_dir, 'weights')

            if os.path.exists(weights_dir):
                logger.info("开始收集关键模型信息（best.pt）...")
                models_info = collect_models_info_on_completion(weights_dir, epoch_metrics_cache, task_name)

                if models_info:
                    # 自动转换模型为OM格式
                    logger.info("开始自动转换模型为OM格式...")
                    models_info = auto_convert_models_to_om(models_info, weights_dir)
                    save_models_info_to_json(models_info)
                    logger.info(f"成功保存 {len(models_info)} 个关键模型的信息")
                else:
                    logger.warning("没有找到关键模型文件（best.pt）")
            else:
                logger.warning(f"模型权重目录不存在: {weights_dir}")

        except Exception as e:
            logger.error(f"收集模型信息失败: {e}")
            logger.error(traceback.format_exc())

        return results
    except Exception as e:
        logger.error(f"训练失败: {e}")
        raise

def get_model_info(model_path, metrics, task_name=None):
    """
    获取模型信息
    Args:
        model_path: 模型文件路径
        metrics: 训练指标
        task_name: 任务名称，用于生成模型名称前缀
    Returns:
        dict: 模型信息
    """
    # 确保model_path是绝对路径
    model_path = os.path.abspath(model_path)

    # 生成模型名称，格式为 task_name + "_" + 原始文件名
    original_filename = os.path.basename(model_path)
    if task_name:
        model_name = f"{task_name}_{original_filename}"
    else:
        model_name = original_filename

    model_info = {
        'model_name': model_name,
        'model_path': model_path,
        'accuracy': 0.0,
        'precision': 0.0,
        'recall': 0.0,
        'inference_speed': 0.0,
        'model_size_mb': 0.0,
        'timestamp': datetime.now().isoformat()
    }
    
    try:
        # 获取模型大小
        if os.path.exists(model_path):
            file_size = os.path.getsize(model_path)
            model_info['model_size_mb'] = round(file_size / (1024 * 1024), 2)
            logger.info(f"模型大小: {model_info['model_size_mb']} MB")
        else:
            logger.warning(f"模型文件不存在: {model_path}")
        
        # 从metrics中获取准确率、精度、召回率（尝试多种可能的键名）
        map50_keys = ['metrics/mAP50(B)', 'metrics/mAP50', 'mAP50', 'val/mAP50']
        precision_keys = ['metrics/precision(B)', 'metrics/precision', 'precision', 'val/precision']
        recall_keys = ['metrics/recall(B)', 'metrics/recall', 'recall', 'val/recall']
       
        model_info['accuracy'] = round(get_metric_value(metrics, map50_keys), 4)
        model_info['precision'] = round(get_metric_value(metrics, precision_keys), 4)
        model_info['recall'] = round(get_metric_value(metrics, recall_keys), 4)

        logger.info(f"提取的指标 - accuracy(mAP50): {model_info['accuracy']}, precision: {model_info['precision']}, recall: {model_info['recall']}")

        # 如果所有指标都为0，打印调试信息
        if model_info['accuracy'] == 0.0 and model_info['precision'] == 0.0 and model_info['recall'] == 0.0:
            logger.warning(f"所有指标都为0，原始metrics内容: {metrics}")
            logger.warning(f"可用的键: {list(metrics.keys()) if metrics else 'metrics为空'}")
        
        # 计算推理速度（这里使用一个估算值，实际应该通过推理测试获得）
        model_size_mb = model_info['model_size_mb']
        if model_size_mb > 0:
            # 简单的推理速度估算（FPS），实际应该通过真实推理测试
            estimated_fps = max(10, 100 - model_size_mb * 2)  # 模型越大，FPS越低
            model_info['inference_speed'] = round(estimated_fps, 2)
        
        logger.info(f"获取模型信息成功: {model_info}")
        
    except Exception as e:
        logger.error(f"获取模型信息失败: {e}")
        logger.error(traceback.format_exc())
    
    return model_info

def get_best_metrics_from_cache(epoch_metrics_cache):
    """
    从epoch指标缓存中获取最佳指标
    Args:
        epoch_metrics_cache: epoch指标缓存字典
    Returns:
        dict: 包含最佳指标的字典
    """
    if not epoch_metrics_cache:
        return {}

    best_metrics = {}

    # 初始化最佳值
    best_map50 = 0.0
    best_precision = 0.0
    best_recall = 0.0
    best_map50_95 = 0.0

    # 遍历所有epoch，找到最佳指标
    for epoch, metrics in epoch_metrics_cache.items():
        # 检查不同可能的键名格式
        map50_keys = ['metrics/mAP50(B)', 'metrics/mAP50', 'mAP50', 'val/mAP50']
        precision_keys = ['metrics/precision(B)', 'metrics/precision', 'precision', 'val/precision']
        recall_keys = ['metrics/recall(B)', 'metrics/recall', 'recall', 'val/recall']
        map50_95_keys = ['metrics/mAP50-95(B)', 'metrics/mAP50-95', 'mAP50-95', 'val/mAP50-95']

        # 获取当前epoch的指标值
        current_map50 = get_metric_value(metrics, map50_keys)
        current_precision = get_metric_value(metrics, precision_keys)
        current_recall = get_metric_value(metrics, recall_keys)
        current_map50_95 = get_metric_value(metrics, map50_95_keys)

        logger.info(f"Epoch {epoch} 指标: mAP50={current_map50:.4f}, precision={current_precision:.4f}, recall={current_recall:.4f}")

        # 更新最佳值（主要以mAP50为准）
        if current_map50 > best_map50:
            best_map50 = current_map50
            best_precision = current_precision
            best_recall = current_recall
            best_map50_95 = current_map50_95

            logger.info(f"发现更好的指标在Epoch {epoch}: mAP50={best_map50:.4f}, precision={best_precision:.4f}, recall={best_recall:.4f}")

    # 构建最佳指标字典（使用标准键名）
    best_metrics = {
        'metrics/mAP50(B)': best_map50,
        'metrics/precision(B)': best_precision,
        'metrics/recall(B)': best_recall,
        'metrics/mAP50-95(B)': best_map50_95
    }

    logger.info(f"最佳指标汇总: {best_metrics}")
    return best_metrics

def get_metric_value(metrics, possible_keys):
    """
    从指标字典中获取值，尝试多个可能的键名
    Args:
        metrics: 指标字典
        possible_keys: 可能的键名列表
    Returns:
        float: 指标值，如果找不到则返回0.0
    """
    for key in possible_keys:
        if key in metrics:
            value = metrics[key]
            if isinstance(value, (int, float)):
                return float(value)
    return 0.0

def collect_models_info_on_completion(weights_dir, epoch_metrics_cache, task_name):
    """
    训练完成时收集模型信息的简化版本
    Args:
        weights_dir: 模型权重目录
        epoch_metrics_cache: epoch指标缓存
        task_name: 任务名称
    Returns:
        list: 模型信息列表
    """
    models_info = []

    if not epoch_metrics_cache:
        logger.warning("没有epoch指标缓存，无法生成模型信息")
        return models_info

    # 获取最佳指标
    best_epoch_metrics = get_best_epoch_metrics(epoch_metrics_cache)

    logger.info(f"最佳指标来自: Epoch {best_epoch_metrics.get('epoch', 'unknown')}")

    # 只处理best.pt
    best_pt_path = os.path.join(weights_dir, 'best.pt')
    if os.path.exists(best_pt_path):
        best_model_info = create_model_info(best_pt_path, best_epoch_metrics, task_name, 'best.pt')
        if best_model_info:
            models_info.append(best_model_info)
            logger.info(f"成功创建best.pt模型信息")

    return models_info

def get_best_epoch_metrics(epoch_metrics_cache):
    """
    从epoch缓存中找到最佳指标的epoch
    Args:
        epoch_metrics_cache: epoch指标缓存
    Returns:
        dict: 最佳epoch的指标数据
    """
    best_map50 = 0.0
    best_epoch_data = None

    # 定义可能的mAP50键名
    map50_keys = ['metrics/mAP50(B)', 'metrics/mAP50', 'mAP50', 'val/mAP50']

    for epoch, metrics in epoch_metrics_cache.items():
        # 获取当前epoch的mAP50值
        current_map50 = 0.0
        for key in map50_keys:
            if key in metrics and isinstance(metrics[key], (int, float)):
                current_map50 = float(metrics[key])
                break

        logger.info(f"Epoch {epoch} mAP50: {current_map50:.4f}")

        # 更新最佳值
        if current_map50 > best_map50:
            best_map50 = current_map50
            best_epoch_data = metrics.copy()
            logger.info(f"发现更好的指标在Epoch {epoch}: mAP50={best_map50:.4f}")

    if best_epoch_data:
        logger.info(f"最佳指标: mAP50={best_map50:.4f}")
        return best_epoch_data
    else:
        logger.warning("未找到有效的最佳指标，使用最后一轮数据")
        return epoch_metrics_cache[max(epoch_metrics_cache.keys())]

def create_model_info(model_path, metrics_data, task_name, model_type):
    """
    创建单个模型的信息
    Args:
        model_path: 模型文件路径
        metrics_data: 指标数据
        task_name: 任务名称
        model_type: 模型类型 ('last.pt' 或 'best.pt')
    Returns:
        dict: 模型信息字典
    """
    try:
        # 基础模型信息
        model_info = {
            'model_name': f"{task_name}_{model_type.replace('.pt', '')}",
            'model_path': model_path,
            'model_type': model_type,
            'task_name': task_name,
            'created_at': datetime.now().isoformat(),
        }

        # 获取文件大小
        if os.path.exists(model_path):
            model_size_bytes = os.path.getsize(model_path)
            model_info['model_size_mb'] = round(model_size_bytes / (1024 * 1024), 2)
        else:
            model_info['model_size_mb'] = 0.0
            logger.warning(f"模型文件不存在: {model_path}")

        # 从metrics_data中提取指标（过滤掉非指标字段）
        filtered_metrics = {k: v for k, v in metrics_data.items()
                          if k not in ['epoch', 'timestamp'] and isinstance(v, (int, float))}

        # 定义指标键名
        map50_keys = ['metrics/mAP50(B)', 'metrics/mAP50', 'mAP50', 'val/mAP50']
        precision_keys = ['metrics/precision(B)', 'metrics/precision', 'precision', 'val/precision']
        recall_keys = ['metrics/recall(B)', 'metrics/recall', 'recall', 'val/recall']

        # 提取指标值
        model_info['accuracy'] = round(get_metric_value(filtered_metrics, map50_keys), 4)
        model_info['precision'] = round(get_metric_value(filtered_metrics, precision_keys), 4)
        model_info['recall'] = round(get_metric_value(filtered_metrics, recall_keys), 4)

        # 估算推理速度
        model_size_mb = model_info['model_size_mb']
        if model_size_mb > 0:
            estimated_fps = max(10, 100 - model_size_mb * 2)
            model_info['inference_speed'] = round(estimated_fps, 2)
        else:
            model_info['inference_speed'] = 0.0

        logger.info(f"{model_type} 指标 - accuracy: {model_info['accuracy']}, precision: {model_info['precision']}, recall: {model_info['recall']}")

        # 如果所有指标都为0，打印调试信息
        if model_info['accuracy'] == 0.0 and model_info['precision'] == 0.0 and model_info['recall'] == 0.0:
            logger.warning(f"{model_type} 所有指标都为0")
            logger.warning(f"原始metrics_data: {metrics_data}")
            logger.warning(f"过滤后的指标: {filtered_metrics}")
            logger.warning(f"可用的键: {list(filtered_metrics.keys())}")

        return model_info

    except Exception as e:
        logger.error(f"创建{model_type}模型信息失败: {e}")
        return None

def collect_key_model_info(weights_dir, epoch_metrics_cache, task_name):
    """
    收集训练完成后的关键模型信息（只获取best.pt）
    Args:
        weights_dir: 模型权重目录
        epoch_metrics_cache: 缓存的epoch指标数据
        task_name: 任务名称
    Returns:
        list: 模型信息列表
    """
    models_info = []

    try:
        # 只关注best.pt模型
        model_file = 'best.pt'
        model_path = os.path.join(weights_dir, model_file)

        # 检查文件是否存在且不为空
        if not os.path.exists(model_path):
            logger.warning(f"关键模型文件不存在: {model_file}")
            return models_info

        if os.path.getsize(model_path) == 0:
            logger.warning(f"关键模型文件为空: {model_file}")
            return models_info

        # 获取对应的指标
        model_metrics = {}
        if epoch_metrics_cache:
            # best.pt使用所有epoch中的最佳指标
            model_metrics = get_best_metrics_from_cache(epoch_metrics_cache)
            logger.info(f"best.pt使用所有epoch中的最佳指标: {model_metrics}")
        else:
            logger.info(f"没有缓存指标，{model_file}使用默认值")

        # 构建模型信息
        model_info = get_model_info(model_path, model_metrics, task_name)

        # 标记模型类型
        model_info['is_best'] = True
        model_info['model_type'] = 'best'
        logger.info(f"标记为最佳模型: {model_info['model_name']}")

        # 添加训练完成时间
        model_info['training_completed'] = datetime.now().isoformat()

        models_info.append(model_info)
        logger.info(f"成功收集关键模型信息: {model_file} -> {model_info['model_name']}")

        logger.info(f"成功收集模型信息")

    except Exception as e:
        logger.error(f"收集关键模型信息时出错: {e}")
        logger.error(traceback.format_exc())

    return models_info

def save_models_info_to_json(models_info):
    """
    保存多个模型信息到  training_module.json 文件
    Args:
        models_info: 模型信息字典列表
    """
    try:
        # 构建文件路径 - 使用当前目录而非固定的/workspace路径
        current_dir = os.getcwd()
        json_file_path = os.path.join(current_dir, "training_module.json")
        logger.info(f"准备保存 {len(models_info)} 个模型信息到: {json_file_path}")
        
        # 读取现有数据（如果存在）
        existing_data = []
        if os.path.exists(json_file_path):
            try:
                with open(json_file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if content.strip():  # 确保文件不为空
                        existing_data = json.loads(content)
                    logger.info(f"成功读取现有数据，包含 {len(existing_data)} 条记录")
            except json.JSONDecodeError as e:
                logger.warning(f"JSON解析错误，创建新文件: {e}")
                existing_data = []
            except FileNotFoundError:
                logger.warning(f"文件不存在，将创建新文件")
                existing_data = []
            except Exception as e:
                logger.error(f"读取文件时出错: {e}")
                existing_data = []
        
        # 为每个模型更新或添加记录
        updated_models = set()
        for model_info in models_info:
            model_name = model_info['model_name']
            updated = False
            
            # 检查是否已存在相同模型名称的记录
            for i, item in enumerate(existing_data):
                if item.get('model_name') == model_name:
                    # 更新现有记录
                    existing_data[i] = model_info
                    updated = True
                    logger.info(f"更新现有模型记录: {model_name}")
                    break
            
            if not updated:
                # 添加新记录
                existing_data.append(model_info)
                logger.info(f"添加新模型记录: {model_name}")
            
            updated_models.add(model_name)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(json_file_path) or '.', exist_ok=True)
        
        # 保存到文件 - 使用临时文件确保写入安全
        temp_file = json_file_path + '.tmp'
        with open(temp_file, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, indent=2, ensure_ascii=False)
            f.flush()
            os.fsync(f.fileno())  # 确保数据写入磁盘
        
        # 安全地替换原文件
        if os.path.exists(temp_file):
            if os.path.exists(json_file_path):
                os.replace(temp_file, json_file_path)  # 原子操作替换文件
            else:
                os.rename(temp_file, json_file_path)
            
            logger.info(f"模型信息已成功保存到 {json_file_path}，更新了 {len(updated_models)} 个模型")
        else:
            logger.error(f"临时文件未创建成功: {temp_file}")
        
    except Exception as e:
        logger.error(f"保存模型信息到JSON失败: {e}")
        logger.error(traceback.format_exc())


def check_existing_model_info(model_name):
    """
    检查模型是否已存在于training_module.json中
    Args:
        model_name: 模型文件名
    Returns:
        bool: 如果存在返回True，否则返回False
    """
    try:
        # 构建文件路径 - 使用当前目录而非固定的/workspace路径
        current_dir = os.getcwd()
        json_file_path = os.path.join(current_dir, "training_module.json")
        
        # 如果文件不存在，返回False
        if not os.path.exists(json_file_path):
            return False
        
        # 读取现有数据
        with open(json_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if not content.strip():  # 文件为空
                return False
                
            existing_data = json.loads(content)
            
            # 检查是否存在相同名称的模型
            for item in existing_data:
                if item.get('model_name') == model_name:
                    return True
            
            return False
            
    except Exception as e:
        logger.error(f"检查模型是否存在时出错: {e}")
        return False  # 出错时默认返回False，允许保存

def setup_environment(mount_path=None):
    """设置Python环境和路径"""
    # 设置默认路径
    if mount_path is None:
        mount_path = '/root/siton-data-c16a16a2cdc44452a1c4267121b485aa/data/ultralytics_v8'

    # 现在mount_path直接就是ultralytics_dir的完整路径
    ultralytics_dir = mount_path

    # 添加到Python路径（避免重复添加）
    ultralytics_str = str(ultralytics_dir)
    if ultralytics_str not in sys.path:
        sys.path.insert(0, ultralytics_str)
        print(f"✅ 已添加ultralytics路径到sys.path: {ultralytics_str}")

    # 设置环境变量
    current_pythonpath = os.environ.get("PYTHONPATH", "")
    if ultralytics_str not in current_pythonpath:
        if current_pythonpath:
            os.environ["PYTHONPATH"] = f"{ultralytics_str}:{current_pythonpath}"
        else:
            os.environ["PYTHONPATH"] = ultralytics_str
        print(f"✅ 已设置PYTHONPATH环境变量")

    return ultralytics_dir


def auto_convert_models_to_om(models_info, weights_dir, chip_name="910B3"):
    """
    自动将训练完成的模型转换为OM格式

    Args:
        models_info: 模型信息列表
        weights_dir: 权重文件目录
        chip_name: 芯片型号，默认为910B3

    Returns:
        list: 更新后的模型信息列表，包含初始转换状态
    """
    logger.info(f"🔄 开始转换模型为OM格式，芯片型号: {chip_name}")
    
    # 复制模型信息，避免修改原始数据
    updated_models_info = copy.deepcopy(models_info)
    
    # 标记转换状态为"进行中"
    for model_info in updated_models_info:
        model_info['conversion_status'] = 'pending'
        model_info['is_converted'] = False
    
    # 保存初始状态
    save_models_info_to_json(updated_models_info)
    
    # 模型进行转换
    logger.info(f"🚀 模型开始转换，共 {len(updated_models_info)} 个模型")
    _run_model_conversion(updated_models_info, weights_dir, chip_name)
  
    return updated_models_info

def _run_model_conversion(models_info, weights_dir, chip_name):
    """
    后台执行模型转换的函数
    
    Args:
        models_info: 模型信息列表
        weights_dir: 权重文件目录
        chip_name: 芯片型号
    """
    try:
        # 导入必要的模块
        import sys
        import subprocess
        from pathlib import Path
        
        # 添加ultralytics路径
        ultralytics_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "ultralytics_v8")
        if os.path.exists(ultralytics_dir):
            sys.path.insert(0, str(ultralytics_dir))
            os.environ["PYTHONPATH"] = str(ultralytics_dir)
        try:
            from ultralytics import YOLO
        except ImportError as e:
            logger.error(f"导入模块失败: {e}")
            for model_info in models_info:
                model_info['conversion_status'] = 'failed'
                model_info['error'] = f'导入模块失败: {str(e)}'
            save_models_info_to_json(models_info)
            return
        
        for i, model_info in enumerate(models_info):
            try:
                model_path = model_info.get('model_path')
                model_name = model_info.get('model_name', '')
                
                if not model_path or not os.path.exists(model_path):
                    logger.warning(f"模型文件不存在: {model_path}")
                    model_info['conversion_status'] = 'failed'
                    model_info['error'] = '模型文件不存在'
                    save_models_info_to_json(models_info)
                    continue
                
                logger.info(f"🚀 开始转换模型 ({i+1}/{len(models_info)}): {model_name}")
                model_info['conversion_status'] = 'converting'
                
                # 保存中间状态
                save_models_info_to_json(models_info)
                
                # 转换为ONNX和OM格式
                onnx_path, om_path = convert_pt_to_om(model_path, chip_name)
                
                if onnx_path:
                    model_info['onnx_path'] = onnx_path
                    logger.info(f"✅ ONNX转换成功: {onnx_path}")
                
                if om_path:
                    model_info['om_path'] = om_path
                    model_info['is_converted'] = True
                    model_info['conversion_status'] = 'success'
                    model_info['converted_at'] = datetime.now().isoformat()
                    logger.info(f"✅ OM转换成功: {om_path}")
                else:
                    model_info['is_converted'] = False
                    model_info['conversion_status'] = 'failed'
                    logger.warning(f"⚠️ OM转换失败: {model_name}")
                
                # 保存每个模型的转换结果
                save_models_info_to_json(models_info)
                
            except Exception as e:
                logger.error(f"转换模型失败 {model_info.get('model_name', 'Unknown')}: {e}")
                model_info['is_converted'] = False
                model_info['conversion_status'] = f'error: {str(e)}'
                save_models_info_to_json(models_info)
        
        logger.info(f"🎯 后台模型转换完成，共处理 {len(models_info)} 个模型")
        
    except Exception as e:
        logger.error(f"模型转换线程出错: {e}")
        logger.error(traceback.format_exc())
        
        # 更新所有未完成转换的模型状态
        for model_info in models_info:
            if model_info.get('conversion_status') in ['pending', 'converting']:
                model_info['conversion_status'] = 'failed'
                model_info['error'] = f'转换线程出错: {str(e)}'
        
        save_models_info_to_json(models_info)


def convert_pt_to_om(model_path, chip_name="910B3"):
    """
    将PyTorch模型转换为ONNX和OM格式

    Args:
        model_path: 模型文件路径
        chip_name: 芯片型号

    Returns:
        tuple: (onnx_path, om_path) 转换后的ONNX和OM模型路径
    """
    try:
        from ultralytics import YOLO

        # 获取模型名称（不带扩展名）
        model_name = os.path.splitext(os.path.basename(model_path))[0]
        model_dir = os.path.dirname(model_path)
        current_dir = os.getcwd()

        logger.info(f"开始转换模型: {model_path}")

        # 1. 加载模型并导出为ONNX格式
        model = YOLO(model_path)
        onnx_model = model.export(format="onnx", dynamic=True, simplify=True, opset=11)
        
        # 确保ONNX路径是绝对路径
        if not os.path.isabs(onnx_model):
            onnx_model = os.path.abspath(onnx_model)
            
        logger.info(f"ONNX模型已导出: {onnx_model}")

        # 2. 检查是否需要安装必要的包（在NPU环境中）
        install_npu_packages_if_needed()

        # 3. 转换为OM格式
        om_path = convert_onnx_to_om_format(onnx_model, model_name, chip_name)

        return onnx_model, om_path

    except Exception as e:
        logger.error(f"模型转换失败: {e}")
        return None, None


def install_npu_packages_if_needed():
    """
    检查并安装NPU相关的包（如果需要）
    """
    try:
        # 检查是否在NPU环境中
        if not os.environ.get('CANN_HOME') and not os.path.exists('/usr/local/Ascend'):
            logger.info("非NPU环境，跳过NPU包安装")
            return

        # NPU环境中需要的包
        data_dir = "/root/siton-data-b496463103254f46976c4ff88ea74bc9/data"
        pip_packages = [
            os.path.join(data_dir, "aclruntime-0.0.2-cp39-cp39-linux_aarch64.whl"),
            os.path.join(data_dir, "ais_bench-0.0.2-py3-none-any.whl")
        ]

        for package in pip_packages:
            if os.path.exists(package):
                try:
                    logger.info(f"正在安装NPU包: {package}")
                    result = subprocess.run(
                        f"pip install {package}",
                        shell=True,
                        check=True,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True
                    )
                    logger.info(f"NPU包安装成功: {package}")
                except subprocess.CalledProcessError as e:
                    logger.warning(f"NPU包安装失败: {package}, 错误: {e.stderr}")
            else:
                logger.warning(f"NPU包不存在: {package}")

    except Exception as e:
        logger.warning(f"安装NPU包时出错: {e}")


def convert_onnx_to_om_format(onnx_model, model_name, chip_name="910B3"):
    """
    使用ATC工具将ONNX模型转换为OM格式

    Args:
        onnx_model: ONNX模型路径
        model_name: 模型名称
        chip_name: 芯片型号
    Returns:
        str: OM模型路径，转换失败返回None
    """
    try:
        # 设置参数
        batchsize = 1
        # 构建输出OM模型路径 - 使用绝对路径
        current_dir = os.getcwd()
        om_model_name = f"{model_name}_bs{batchsize}.om"
        om_model = os.path.join(current_dir, om_model_name)
        
        # 确保ONNX模型路径是绝对路径
        if not os.path.isabs(onnx_model):
            onnx_model = os.path.abspath(onnx_model)

        # 构建atc命令
        atc_cmd = f"""atc --framework=5 \
--model={onnx_model} \
--input_format=NCHW \
--input_shape="images:{batchsize},3,640,640" \
--output_type=FP16 \
--output={os.path.join(current_dir, model_name)}_bs{batchsize} \
--log=error \
--soc_version=Ascend{chip_name}"""

        logger.info(f"执行ATC命令: {atc_cmd}")

        # 设置环境变量
        env = os.environ.copy()
        if os.path.exists('/usr/local/Ascend/ascend-toolkit/set_env.sh'):
            # 在NPU环境中设置环境变量
            env_setup = 'source ~/.bashrc && source /usr/local/Ascend/ascend-toolkit/set_env.sh'
            full_cmd = f"bash -c '{env_setup} && {atc_cmd}'"
        else:
            full_cmd = atc_cmd

        # 执行atc命令
        result = subprocess.run(
            full_cmd,
            shell=True,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            env=env
        )

        # 检查OM文件是否生成
        if os.path.exists(om_model):
            logger.info(f"ATC转换成功: {om_model}")
            logger.info(f"转换输出: {result.stdout}")
            return om_model
        else:
            logger.error(f"ATC转换失败，未找到输出文件: {om_model}")
            return None

    except subprocess.CalledProcessError as e:
        logger.error(f"ATC转换失败: {e}")
        logger.error(f"错误信息: {e.stderr}")
        return None
    except Exception as e:
        logger.error(f"转换过程中出错: {e}")
        return None


def main():
    """主函数"""
    print("🚀 华为NPU单机多卡YOLO训练")
    print(f"📁 工作目录: {Path(__file__).parent.absolute()}")

    # 先解析参数
    args = parse_arguments() 

    # 根据参数设置环境
    ultralytics_dir = setup_environment(
        mount_path=getattr(args, 'mount_path', None)
    )

    # 现在可以安全导入ultralytics
    try:
        from ultralytics import YOLO
        print(f"\n✅ 使用ultralytics版本: {YOLO.__module__}")
        print(f"✅ ultralytics路径: {ultralytics_dir}")
    except ImportError as e:
        print(f"\n❌ ultralytics导入失败: {e}")
        print(f"请确保ultralytics_v8目录存在: {ultralytics_dir}")
        sys.exit(1)

    # 读取配置文件
    try:
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(f"✅ 成功加载配置文件: {args.config}")
    except FileNotFoundError:
        print(f"❌ 配置文件不存在: {args.config}")
        print("💡 请确保配置文件存在，或使用 --config 参数指定正确路径")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件格式错误: {e}")
        sys.exit(1)
    
    try:
        
        # 检查NPU环境
        print("\n" + "="*50)
        print("🔧 检查NPU环境")
        print("="*50)
        setup_npu_environment()
        
        # 配置设备
        print("\n" + "="*50)
        print("⚙️ 配置训练设备")
        print("="*50)
        device = detect_device()  # 自动检测设备

        # 解析训练配置
        train_args = parse_training_config(config, args.data, device, args.task_id)

        # 命令行参数覆盖配置文件参数
        if args.model:
            model_path = args.model
        else:
            model_path = config.get('model', {}).get('path', 'yolov8n.pt')

        # 处理续训参数
        resume_training = False
        if args.resume:
            resume_training = True
            # 续训时使用last.pt
            task_name = train_args.get('name', 'task_default')
            last_model_path = f'/workspace/runs_detect/{task_name}/weights/last.pt'
            if os.path.exists(last_model_path):
                model_path = last_model_path
                print(f"✅ 找到最新模型用于续训: {last_model_path}")
            else:
                print(f"⚠️ 找不到续训模型: {last_model_path}")
                print("   将使用原始模型开始续训")

        print(f"📋 训练配置:")
        print(f"   模型: {model_path}")
        print(f"   数据集: {train_args['data']}")
        print(f"   轮次: {train_args['epochs']}")
        print(f"   批次大小: {train_args['batch']}")
        print(f"   设备: {device}")
        print(f"   续训模式: {'是' if resume_training else '否'}")

        # 启动单机训练
        single_machine_train(train_args, model_path, device, resume_training)
        print("✅ 所有训练任务完成！")

    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
