<!--
 * @Author: Szc
 * @Date: 2025-08-14 10:32:11
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-29 10:13:16
 * @Description: 
-->

<template>
    <div class="content">
        <div class="top">
            <div class="left">
                <div class="one">
                    <img class="samllIcon" src="../../assets/images/icon_dz.png" alt="">
                    <div class="labelColor title">请选择数据集进行加载与分割</div>
                </div>
                <div class="two">
                    <div class="label">选择数据集:</div>
                    <q-select class="cusQselect" v-model="algorithm.modelPath" :options="modelOptions"
                        :label="algorithm.modelPath ? '' : '请选择数据集'" :loading="loadingDatasets" outlined dense
                        emit-value map-options @update:model-value="onDatasetChange" />
                </div>
                <div class="three">
                    <div class="label">数据集分割：</div>
                    <div class="blueT">训练集占比</div>
                    <q-input class="percent" v-model="percent" outlined dense
                        @update:model-value="updatePercentages"></q-input>
                    <el-slider class="slider" v-model="percent" @change="updatePercentages" />
                    <div class="blueT ml20">验证集占比</div>
                    <q-input class="percent" v-model="percentEnd" outlined dense disabled></q-input>
                </div>
                <div class="four">
                    将数据集按比例随机切分为训练集、验证集和测试集，其中训练集用于模型训练，验证集用于验证模型训练性能，测试集用于模型试验，切分占比1-99之间，不可为0
                </div>
                <div class="five">
                    <q-btn v-if="!shouldHideAnalysisButton" 
                           class="startBtn roundBox" 
                           :color="isAnalysisInProgress ? 'grey-7' : 'primary'" 
                           :disabled="isAnalysisInProgress"
                           :label="isAnalysisInProgress ? '分析中...' : '开始分析'" 
                           @click="startAnalysis" />
                </div>
            </div>
            <div class="right">
                <div class="one">
                    <img class="samllIcon" src="../../assets/images/icon_dz.png" alt="">
                    <div class="labelColor title">数据集分析结果</div>
                </div>
                <div class="step custom-steps">
                    <el-steps align-center :active="active" finish-status="success" class="green-success-steps">
                        <el-step title="提交任务" />
                        <el-step title="数据集加载" />
                        <el-step title="数据集分割" />
                        <el-step title="数据集保存" />
                        <el-step title="完成" />
                    </el-steps>
                </div>
                <div class="end">
                    <div class="end-left" v-if="showResults">
                        <img src="../../assets/images/iconDone.png" alt="" class="done">
                        <div class="status">数据校验通过</div>
                    </div>
                    <div class="end-right" v-if="showResults">
                        <div class="one">
                            <div class="name">训练集:</div>
                            <div class="key">样本</div>
                            <q-input class="val mr20" v-model="trainNumber" outlined dense></q-input>
                            <div class="key">占比</div>
                            <q-input class="val" v-model="trainPercentDisplay" outlined dense></q-input>
                        </div>
                        <div class="one">
                            <div class="name">验证集:</div>
                            <div class="key">样本</div>
                            <q-input class="val mr20" v-model="yzjNumber" outlined dense></q-input>
                            <div class="key">占比</div>
                            <q-input class="val" v-model="valPercentDisplay" outlined dense></q-input>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bottom">
            <div class="title">
                <q-btn class="default" :class="{ 'act': datasetType === 'train' }"
                    @click="switchDatasetType('train')">训练集</q-btn>
                <q-btn class="default" :class="{ 'act': datasetType === 'val' }"
                    @click="switchDatasetType('val')">验证集</q-btn>
                <q-btn class="default" :class="{ 'act': datasetType === 'category' }"
                    @click="switchDatasetType('category')">类别分类图</q-btn>
                <div class="title-r">抽样展示10个样本</div>
            </div>
            <div class="swipe" @click="resetMarquee">
                <!-- 加载状态 -->
                <div v-if="loadingImages" class="loading-container">
                    <q-spinner-dots size="2em" color="primary" />
                    <div class="loading-text">正在加载数据...</div>
                </div>

                <!-- 类别分类图 -->
                <div v-else-if="datasetType === 'category'" class="chart-container" ref="chartContainer"></div>

                <!-- 数据集样本跑马灯 -->
                <div v-else class="sample-marquee-content" ref="sampleMarqueeContent"
                    :style="{ transform: `translateX(-${sampleTranslateX}px)` }">
                    <div class="quard-item marquee-item" v-for="(sample, index) in displayedSamples" :key="index"
                        @click="toggleImageSize(sample)"
                        :class="{ 'enlarged': selectedSample === sample || isCenterImage(index) }">
                        <q-card class="algorithm-card" :class="{ 'selected-algorithm': currentSample === sample.id }"
                            @click="currentSample = sample.id">
                            <q-img :src="sample.image_url || defaultBackground" style="height:100%">
                                <template v-slot:error>
                                    <div class="absolute-full flex flex-center bg-grey-3 text-grey-7">
                                        <q-icon name="image" size="2em" />
                                    </div>
                                </template>
                            </q-img>
                            <div class="imgType">{{ sample.version }}</div>
                            <div class="imgName">{{ sample.name }}</div>
                        </q-card>
                    </div>
                </div>
            </div>
        </div>
        <div class="next">
            <q-btn v-if="false" class="prevBtn roundBox" color="grey-7" label="上一步" @click="prevStep" />
            <q-btn 
                class="nextBtn roundBox" 
                :color="canProceedToNext ? 'primary' : 'grey-7'" 
                :disabled="!canProceedToNext"
                label="下一步" 
                @click="nextStep" 
            />
        </div>

    </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, nextTick, defineEmits, watch } from 'vue'
import { api } from 'boot/axios'
import { usePlugin } from 'composables/plugin.js'
import { useLargeModelStore } from '../../stores/largeModelStore'
import { startDataAnalysis, saveStepData } from '../../request/workFlow/index.js'
import * as echarts from 'echarts'
import defaultBackground from '../../assets/images/smart_model_backgroud.png'
import algoImg from '../../assets/images/algo.png'

// 定义事件
const emit = defineEmits(['next-step', 'prev-step'])

// 引入通知插件
const { notify } = usePlugin()

// 获取大模型表单存储
const largeModelStore = useLargeModelStore()

// 随机生成70-80之间的训练集占比
function getRandomPercent() {
    return Math.floor(Math.random() * 11) + 70 // 70-80之间的随机整数
}

// 训练集占比
let percent = ref(getRandomPercent())
// 验证集占比
let percentEnd = ref(100 - percent.value)
// 步骤索引
let active = ref(0)
// 是否显示结果
let showResults = ref(false)

// 样本数据和奖励数量 - 固定值
let trainPercentDisplay = ref(percent.value + '%')
let valPercentDisplay = ref(percentEnd.value + '%')

// 实际样本数（从API获取）
let actualTrainSamples = ref(null)
let actualValSamples = ref(null)

let trainNumber = computed(() => {
    // 如果有API返回的实际样本数，使用实际值；否则使用百分比
    return actualTrainSamples.value !== null ? actualTrainSamples.value : percent.value
})

let yzjNumber = computed(() => {
    // 如果有API返回的实际样本数，使用实际值；否则使用百分比
    return actualValSamples.value !== null ? actualValSamples.value : percentEnd.value
})

// 总样本数计算属性
let total_samples = computed(() => {
    return percent.value + percentEnd.value
})

// 计算是否正在进行分析（用于禁用开始分析按钮）
const isAnalysisInProgress = computed(() => {
    // 如果分析步骤大于0但小于5，说明正在进行分析
    return active.value > 0 && active.value < 5
})

// 计算是否可以进入下一步
const canProceedToNext = computed(() => {
    // 必须选择了数据集
    if (!selectedDatasetName.value) return false
    
    // 必须完成数据分析
    if (!showResults.value) return false
    
    // 分析步骤必须完成
    if (active.value < 5) return false
    
    return true
})

// 计算是否应该隐藏开始分析按钮
const shouldHideAnalysisButton = computed(() => {
    // 如果是通过jump进入且已经完成分析，则隐藏按钮
    return isFromJump.value && showResults.value && active.value >= 5
})

// 更新百分比
function updatePercentages(value) {
    // 确保训练集占比在1-99之间
    if (value < 1) percent.value = 1
    if (value > 99) percent.value = 99

    // 更新验证集占比
    percentEnd.value = 100 - percent.value

    // 更新显示值
    trainPercentDisplay.value = percent.value + '%'
    valPercentDisplay.value = percentEnd.value + '%'
    
    // 如果比例发生变化，重置分析状态（除非正在恢复数据）
    if (!isRestoringState.value) {
        console.log('执行了')
        active.value = 0
        showResults.value = false
        actualTrainSamples.value = null
        actualValSamples.value = null
    }
}

// 开始分析
async function startAnalysis() {
    // 检查是否已选择数据集
    if (!selectedDatasetName.value) {
        notify('请先选择大模型数据集', 'warning')
        return
    }

    // 检查是否有工作流任务ID，如果没有则提示用户需要先创建任务
    const taskId = largeModelStore.stepThreeData.taskId
    if (!taskId) {
        notify('请先创建训练任务', 'warning')
        return
    }

    // 防止重复分析
    if (isAnalysisInProgress.value) {
        notify('数据分析正在进行中，请等待完成', 'warning')
        return
    }

    try {
        // 重置步骤和显示状态
        active.value = 0
        showResults.value = false

        // 更新显示的百分比
        trainPercentDisplay.value = percent.value + '%'
        valPercentDisplay.value = percentEnd.value + '%'

        // 保存数据到大模型store
        largeModelStore.updateStepOneData({
            datasetName: selectedDatasetName.value,
            trainPercent: percent.value,
            validationPercent: percentEnd.value
        })

        // 调用开始数据分析接口
        const analysisResponse = await startDataAnalysis({
            task_id: taskId,
            dataset_config: {
                dataset_id: selectedDatasetName.value,
                analysis_type: "basic", // 写死为basic
                total_samples: total_samples.value,
                train_ratio: percent.value,
                val_ratio: percentEnd.value
            }
        })

        if (analysisResponse.success) {
            // 如果API返回了分析结果，更新显示的样本数据
            if (analysisResponse.analysis_result) {
                const result = analysisResponse.analysis_result

                // 更新训练集和验证集的实际样本数
                actualTrainSamples.value = result.train_samples
                actualValSamples.value = result.val_samples

                // 更新比例显示
                if (result.train_ratio && result.val_ratio) {
                    trainPercentDisplay.value = result.train_ratio + '%'
                    valPercentDisplay.value = result.val_ratio + '%'
                }
            }

            notify('数据分析已开始', 'positive')
            // 开始模拟步骤进度
            simulateSteps()
        } else {
            notify('数据分析启动失败', 'negative')
        }
    } catch (error) {
        console.error('开始数据分析失败:', error)
        notify('数据分析启动失败: ' + (error.message || '未知错误'), 'negative')

        // 失败时仍然启动模拟进度，以保证UI流程的连贯性
        simulateSteps()
    }
}

// 模拟步骤进度
function simulateSteps() {
    const stepInterval = setInterval(() => {
        if (active.value < 5) {
            active.value++

            // 当到达最后一步时显示结果
            if (active.value === 5) {
                // 延迟显示结果，模拟最后一步完成后的数据校验
                setTimeout(() => {
                    showResults.value = true
                }, 500)

                // 清除定时器
                clearInterval(stepInterval)
            }
        } else {
            clearInterval(stepInterval)
        }
    }, 800) // 每800毫秒进行下一步
}

// 算法配置
const algorithm = ref({
    modelPath: ''
})

// 数据集选项和加载状态
let modelOptions = ref([])
const loadingDatasets = ref(false)
const selectedDatasetName = ref('')

// 是否来自jump进入（有store数据）
const isFromJump = ref(false)
// 是否正在恢复状态（避免在恢复期间重置分析状态）
const isRestoringState = ref(false)

// 跑马灯相关状态
const sampleMarqueeContent = ref(null)
const sampleTranslateX = ref(0)
const sampleSpeed = 3
let sampleIntervalId = null
const selectedSample = ref(null)
const isClick = ref(false)
const isClickItem = ref(null)
const currentSample = ref(null)

// 数据集样本数据 - 从API获取
const sampleData = ref([])
const loadingImages = ref(false)

// 数据集类型（训练集/验证集/类别分类图）
const datasetType = ref('train')

// 图表相关
const chartContainer = ref(null)
let chartInstance = null

// 图表配置（针对大模型数据优化）
const chartOptions = {
    title: {
        text: '大模型数据集类别分布',
        left: 'center',
        textStyle: {
            color: '#fff'
        }
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow'
        }
    },
    grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        data: ['文本数据A', '文本数据B', '验证文本A', '验证文本B', '测试文本'],
        axisLabel: {
            color: '#fff'
        }
    },
    yAxis: {
        type: 'value',
        axisLabel: {
            color: '#fff'
        }
    },
    series: [
        {
            name: '数量',
            type: 'bar',
            barWidth: '20%',
            data: [1200, 2000, 1500, 800, 700],
            itemStyle: {
                color: '#4356F6'
            }
        }
    ]
}

// 初始化图表
function initChart() {
    if (chartContainer.value) {
        // 如果已经存在图表实例，先销毁
        if (chartInstance) {
            chartInstance.dispose()
        }

        // 创建新的图表实例
        chartInstance = echarts.init(chartContainer.value)

        // 设置图表配置
        chartInstance.setOption(chartOptions)

        // 添加窗口大小变化的监听器
        window.addEventListener('resize', handleResize)
    }
}

// 处理窗口大小变化
function handleResize() {
    if (chartInstance) {
        chartInstance.resize()
    }
}

// 更新图表数据
function updateCategoryChart() {
    // 大模型数据类别
    const mockCategories = ['文本数据A', '文本数据B', '验证文本A', '验证文本B', '测试文本']
    const mockCounts = mockCategories.map(() => Math.floor(Math.random() * 2000) + 500) // 500-2499之间的随机整数

    // 更新配置
    const updatedOptions = {
        xAxis: {
            data: mockCategories
        },
        series: [
            {
                data: mockCounts
            }
        ]
    }

    // 更新图表
    if (chartInstance) {
        chartInstance.setOption(updatedOptions)
    } else {
        // 如果图表实例不存在，初始化图表
        nextTick(() => {
            initChart()
        })
    }
}

// 复制数据用于无缝滚动
const displayedSamples = computed(() => [...sampleData.value, ...sampleData.value])

// 跑马灯相关函数（复制自原组件）
function getCardWidth() {
    const card = document.querySelector('.quard-item')
    return card ? card.offsetWidth : 100
}
function getMargin() {
    return parseFloat(getComputedStyle(document.documentElement).fontSize) * 0.5
}
function getContainerWidth() {
    const container = document.querySelector('.swipe')
    return container ? container.offsetWidth : 0
}
function getItemsLen() {
    return sampleData.value.length
}
const centerSampleIndex = computed(() => {
    const cardWidth = getCardWidth()
    const margin = getMargin()
    const itemsLen = getItemsLen()
    const containerWidth = getContainerWidth()
    return Math.floor((sampleTranslateX.value + (containerWidth / 2)) / (cardWidth + margin)) % itemsLen
})

function startMarquee() {
    clearInterval(sampleIntervalId)
    sampleIntervalId = setInterval(() => {
        sampleTranslateX.value += sampleSpeed
        if (sampleMarqueeContent.value && sampleTranslateX.value >= sampleMarqueeContent.value.scrollWidth / 2) {
            sampleTranslateX.value = 0
        }
    }, 20)
}

function toggleImageSize(item) {
    if (selectedSample.value === item) {
        isClick.value = false
        selectedSample.value = null
        startMarquee()
    } else {
        isClick.value = true
        isClickItem.value = item
        selectedSample.value = item
        clearInterval(sampleIntervalId)
    }
}

function resetMarquee() {
    return
}

function isCenterImage(index) {
    const itemsLen = getItemsLen()
    return (index === centerSampleIndex.value || index === centerSampleIndex.value + itemsLen) && !selectedSample.value
}

// API调用函数（适配大模型数据集）
async function fetchDatasets() {
    try {
        loadingDatasets.value = true
        // 调用与StepOne.vue一致的数据集API
        const response = await api.get('backend/datasets/?fields=name')

        if (response && response.results) {
            // 转换为下拉选项格式
            modelOptions.value = response.results.map(dataset => ({
                label: dataset.name,
                value: dataset.name
            }))

            // 如果有数据集，默认选择第一个
            if (modelOptions.value.length > 0) {
                algorithm.value.modelPath = modelOptions.value[0].value
                selectedDatasetName.value = modelOptions.value[0].value
                // 获取第一个数据集的随机图片
                await fetchRandomImages(selectedDatasetName.value, datasetType.value)
            }
        }
    } catch (error) {
        console.error('获取大模型数据集失败:', error)
        notify('获取数据集失败', 'negative')
        // 使用默认数据作为后备
        modelOptions.value = [
            { label: '默认数据集', value: '默认数据集' }
        ]
        // 确保显示默认图片
        sampleData.value = generateDefaultImages()
    } finally {
        loadingDatasets.value = false
    }
}

async function fetchRandomImages(datasetName, type = 'train') {
    if (!datasetName) return

    try {
        loadingImages.value = true
        // 调用与StepOne.vue一致的随机图片API
        const response = await api.get(`backend/datasets/random_images/?dataset_name=${datasetName}`)

        if (response && response.data) {
            // 处理API返回的数据结构
            const dataset = response.data

            // 根据当前选择的数据集类型选择对应的图片数组
            const selectedImages = type === 'train'
                ? (dataset.train_images || [])
                : (dataset.val_images || [])

            // 将图像数据转换为组件需要的格式
            let apiImages = selectedImages.map((image, index) => ({
                id: `img_${index}`,
                name: image.filename || `图片${index + 1}`,
                version: type === 'train' ? '训练集' : '验证集',
                image_url: `data:${image.mime_type || 'image/jpeg'};base64,${image.data}`
            }))

            if (apiImages.length < 10) {
                const defaultImages = generateDefaultImages()
                const remainingCount = 10 - apiImages.length
                const supplementImages = defaultImages.slice(0, remainingCount).map((img, index) => ({
                    ...img,
                    id: `supplement_${index}`,
                    name: `补充图片${index + 1}`
                }))
                sampleData.value = [...apiImages, ...supplementImages]
            } else {
                sampleData.value = apiImages.slice(0, 10)
            }
            if (apiImages.length === 0) {
                sampleData.value = generateDefaultImages()
                notify(`未找到${type === 'train' ? '训练集' : '验证集'}图片，显示默认图片`, 'warning')
            }
        } else {
            sampleData.value = generateDefaultImages()
            notify('未找到数据集图片，显示默认图片', 'warning')
        }

        nextTick(() => {
            startMarquee()
        })

    } catch (error) {
        console.error('获取随机图片失败:', error)
        notify('获取图片失败: ' + (error.message || '未知错误'), 'negative')
        // 使用默认图片数据
        sampleData.value = generateDefaultImages()
    } finally {
        loadingImages.value = false
    }
}

// 生成默认图片数据（确保有10张）
function generateDefaultImages() {
    return Array.from({ length: 10 }, (_, index) => ({
        id: `default_${index + 1}`,
        name: `默认图片${index + 1}`,
        version: '默认数据',
        image_url: algoImg
    }))
}

// 切换数据集类型
function switchDatasetType(type) {
    if (datasetType.value === type) return

    datasetType.value = type

    if (type === 'category') {
        // 显示类别分类图
        nextTick(() => {
            initChart()
            updateCategoryChart()
        })
    } else {
        // 重新加载当前数据集的样本
        if (selectedDatasetName.value) {
            fetchRandomImages(selectedDatasetName.value, type)
        }
    }

    // 重置跑马灯状态
    selectedSample.value = null
    isClickItem.value = null
    isClick.value = false
    sampleTranslateX.value = 0
}

// 监听数据集选择变化
function onDatasetChange(datasetName) {
    selectedDatasetName.value = datasetName

    // 完全重置分析状态
    active.value = 0
    showResults.value = false
    
    // 重置实际样本数
    actualTrainSamples.value = null
    actualValSamples.value = null

    // 更新训练集和验证集比例
    percent.value = getRandomPercent()
    updatePercentages(percent.value)

    // 清空store中的第一步数据（如果有的话）
    largeModelStore.updateStepOneData({
        datasetName: '',
        trainPercent: 0,
        validationPercent: 0,
        analysis_completed: false,
        analysis_steps_completed: 0
    })

    // 根据当前显示模式加载相应数据
    if (datasetType.value === 'category') {
        nextTick(() => {
            updateCategoryChart()
        })
    } else {
        fetchRandomImages(datasetName, datasetType.value)
    }
    
    notify('已切换数据集，请重新进行分析', 'info')
}

// 组件挂载时初始化
onMounted(async () => {
    console.log('大模型第一步组件挂载')
    // 检查是否有store数据（jump进入）
    isFromJump.value = !!(largeModelStore.stepOneData?.datasetName)
    
    if (isFromJump.value) {
        // 设置恢复状态标志，避免在恢复期间重置分析状态
        isRestoringState.value = true
        
        // 如果有store数据，恢复之前的状态
        const storeData = largeModelStore.stepOneData
        percent.value = storeData.trainPercent
        percentEnd.value = storeData.validationPercent
        
        // 恢复分析状态
        if (storeData.analysis_completed) {
            console.log('回显大模型分析结果')
            showResults.value = storeData.analysis_completed
            active.value = storeData.analysis_steps_completed || 5
        }
        
        // 如果有实际样本数，也恢复这些数据
        if (storeData.trainSamples) {
            actualTrainSamples.value = storeData.trainSamples
        }
        if (storeData.validationSamples) {
            actualValSamples.value = storeData.validationSamples
        }
        
        // 恢复百分比显示
        if (storeData.train_percent_display) {
            trainPercentDisplay.value = storeData.train_percent_display
        }
        if (storeData.val_percent_display) {
            valPercentDisplay.value = storeData.val_percent_display
        }
        
        console.log('恢复大模型store中的数据:', storeData)
        
        // 如果没有恢复到百分比显示值，则根据当前比例计算
        if (!storeData.train_percent_display) {
            trainPercentDisplay.value = percent.value + '%'
        }
        if (!storeData.val_percent_display) {
            valPercentDisplay.value = percentEnd.value + '%'
        }
        
        // 恢复完成后清除标志
        isRestoringState.value = false
    } else {
        // 新建任务，使用随机百分比
        percent.value = getRandomPercent()
        percentEnd.value = 100 - percent.value
        
        // 初始化百分比显示（仅在新建任务时）
        updatePercentages(percent.value)
    }

    await fetchDatasets() // 先获取数据集列表
    nextTick(() => {
        if (sampleData.value.length > 0) {
            startMarquee() // 页面加载后立即启动跑马灯
        }
    })
})

// 组件销毁前清理
onBeforeUnmount(() => {
    clearInterval(sampleIntervalId)

    // 移除窗口大小变化的监听器
    window.removeEventListener('resize', handleResize)

    // 销毁图表实例
    if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
    }
})

// 下一步按钮点击事件
async function nextStep() {
    // 检查是否可以进入下一步
    if (!canProceedToNext.value) {
        if (!selectedDatasetName.value) {
            notify('请先选择数据集', 'warning')
            return
        }
        
        if (!showResults.value || active.value < 5) {
            notify('请先完成数据集分析', 'warning')
            return
        }
        
        return
    }

    // 检查是否有工作流任务ID
    const taskId = largeModelStore.stepThreeData.taskId
    if (!taskId) {
        notify('请先创建训练任务', 'warning')
        return
    }

    try {
        // 准备步骤数据 - 包含当前页面的所有展示数据
        const stepData = {
            // 数据集相关
            dataset_name: selectedDatasetName.value,
            dataset_id: selectedDatasetName.value, // 使用数据集名称作为ID
            
            // 数据分割比例
            train_percent: percent.value,
            validation_percent: percentEnd.value,
            train_ratio: percent.value / 100,
            val_ratio: percentEnd.value / 100,
            
            // 样本数量（如果有API返回的实际数据则使用，否则使用计算值）
            train_samples: actualTrainSamples.value || percent.value,
            validation_samples: actualValSamples.value || percentEnd.value,
            total_samples: total_samples.value,
            
            // 分析状态
            analysis_completed: showResults.value,
            analysis_steps_completed: active.value,
            
            // 数据集类型和展示状态
            current_dataset_type: datasetType.value,
            
            // 百分比显示格式
            train_percent_display: trainPercentDisplay.value,
            val_percent_display: valPercentDisplay.value
        }

        // 调用保存步骤数据接口
        const response = await saveStepData({
            task_id: taskId,
            step_number: 1, // 当前是第一步
            step_data: stepData
        })

        if (response.success) {
            // 保存数据到store - 包含完整的状态信息
            largeModelStore.updateStepOneData({
                datasetName: selectedDatasetName.value,
                trainPercent: percent.value,
                validationPercent: percentEnd.value,
                trainSamples: actualTrainSamples.value,
                validationSamples: actualValSamples.value,
                analysis_completed: showResults.value,
                analysis_steps_completed: active.value,
                train_percent_display: trainPercentDisplay.value,
                val_percent_display: valPercentDisplay.value
            })

            // notify('第一步数据保存成功', 'positive')
            emit('next-step')
        } else {
            console.log("保存失败", response.message)
        }
    } catch (error) {
        console.error('保存步骤数据失败:', error)
        
        // 根据不同错误类型提供更具体的错误信息
        let errorMessage = '保存失败'
        if (error.response && error.response.data && error.response.data.message) {
            errorMessage += ': ' + error.response.data.message
        } else if (error.message) {
            errorMessage += ': ' + error.message
        } else {
            errorMessage += ': 网络错误或服务器异常'
        }
        
        // notify(errorMessage, 'negative')
    }
}

// 上一步按钮点击事件  
function prevStep() {
    emit('prev-step')
}

</script>

<style lang="scss" scoped>
// 复用原来的样式，但添加大模型特有的样式修改
.cusQselect {
    width: 3.75rem;

    :deep(.q-field__control-container) {
        padding-top: 0 !important;
    }
}

.label {
    width: 1.3rem;
    color: white;
    font-size: .2rem;
}

.content {
    display: flex;
    flex-direction: column;
    flex: 1;
    position: relative;
    .top {
        display: flex;
        margin-bottom: .125rem;

        .left,
        .right {
            width: 50%;
            min-height: 4.5rem;
            border: .025rem solid #707070;
            background-color: #181a24;
            background-image:
                repeating-linear-gradient(130deg,
                    rgba(255, 255, 255, 0.05) 0px,
                    rgba(255, 255, 255, 0.01) 4px,
                    transparent 1px,
                    transparent 15px);
            padding: .3375rem;

            .one {
                display: flex;
                align-items: center;
                font-size: .225rem;
                margin-bottom: .25rem;

                .samllIcon {
                    width: .2rem;
                    height: .2rem;
                    margin-right: .1875rem;
                }
            }

            .two {
                display: flex;
                align-items: center;
                margin-bottom: .375rem;

                .label {
                    margin-left: .125rem;
                    color: white;
                    font-size: .2rem;
                }
            }

            .three {
                display: flex;
                align-items: center;
                margin-left: .125rem;
                margin-bottom: .375rem;

                .percent {
                    width: 1.25rem;
                    margin: 0 .125rem;
                }

                .slider {
                    width: 2.875rem;
                }

                .blueT {
                    color: #63d4ff;
                    font-size: .2rem;
                }

                .ml20 {
                    margin-left: .25rem;
                }
            }

            .four {
                padding-left: 1.5rem;
                width: 8.55rem;
                color: #cea345;
                font-size: .175rem;
                margin-bottom: .25rem;
            }

            .five {
                display: flex;

                .startBtn {
                    margin-left: auto;
                    width: 1.4rem;
                    height: .5rem;
                    font-size: .2rem;
                }
            }
        }

        .left {
            margin-right: .125rem;
            position: relative;

            &::before {
                position: absolute;
                content: '';
                left: -0.1rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198);
                border-radius: .125rem;
            }

            &::after {
                position: absolute;
                content: '';
                left: -0.075rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198, .5)
            }
        }

        .right {
            position: relative;

            &::before {
                position: absolute;
                content: '';
                left: -0.1rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198);
                border-radius: .125rem;
            }

            &::after {
                position: absolute;
                content: '';
                left: -0.075rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198, .5)
            }

            .step {
                padding: 0 1.875rem;
                margin-bottom: .625rem;

                :deep(.el-step__title) {
                    color: #4ab4ff !important;
                    font-weight: normal !important;
                }
            }

            .end {
                padding-left: .25rem;
                display: flex;

                .end-left {
                    display: flex;
                    align-items: center;
                    color: white;
                    height: .375rem;
                    margin-right: .625rem;

                    .done {
                        width: .375rem;
                        height: .375rem;
                        margin-right: .125rem;
                    }
                }

                .end-right {
                    display: flex;
                    flex-direction: column;

                    .one,
                    .two,
                    .three {
                        display: flex;

                        .name {
                            width: 1.25rem;
                            text-align: right;
                            color: white;
                            margin-right: .375rem;
                        }

                        .key {
                            color: #63d4ff;
                            margin-right: .175rem;
                        }

                        .mr20 {
                            margin-right: .5rem;
                        }

                        .val {
                            width: 1.625rem;
                        }
                    }
                }
            }
        }
    }

    .bottom {
        flex: 1;
        min-height: 5rem;
        border: .025rem solid #707070;
        background-color: #181a24;
        background-image:
            repeating-linear-gradient(130deg,
                rgba(255, 255, 255, 0.05) 0px,
                rgba(255, 255, 255, 0.01) 4px,
                transparent 1px,
                transparent 15px);
        padding: .3rem .375rem;
        position: relative;
        display: flex;
        flex-direction: column;

        &::before {
            position: absolute;
            content: '';
            left: -0.1rem;
            top: 0;
            width: .025rem;
            height: 100%;
            background: rgba(156, 172, 198);
            border-radius: .125rem;
        }

        &::after {
            position: absolute;
            content: '';
            left: -0.075rem;
            top: 0;
            width: .025rem;
            height: 100%;
            background: rgba(156, 172, 198, .5)
        }

        .title {
            display: flex;
            align-items: center;

            .default {
                width: 1.875rem;
                height: .5rem;
                text-align: center;
                font-size: .2rem;
                color: #999;
                background: #b1afad;
                margin-right: .125rem;
            }

            .act {
                color: white;
                background-color: #0b3e50;
            }

            .title-r {
                color: white;
                margin-left: auto;
                font-size: .2rem;
            }
        }

        .swipe {
            flex: 1;
            margin-bottom: .2rem;
            min-height: 2.875rem;
            overflow: hidden;
            position: relative;
            width: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .loading-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 2.875rem;
                width: 100%;

                .loading-text {
                    color: #4ab4ff;
                    font-size: .2rem;
                    margin-top: .25rem;
                }
            }
        }

    }
}

/* 跑马灯样式 */
.sample-marquee-content {
    display: flex;
    white-space: nowrap;
    width: max-content;
    padding: 0;
    list-style-type: none;
    margin: .35rem 0px;
}

.quard-item {
    width: 4.5rem;
    height: 2.7rem;
    margin-right: .5rem;
    border-radius: .0625rem;
}

.marquee-item {
    display: inline-block;
    transition: transform 0.3s;
}

.marquee-item:hover {
    cursor: pointer;
    transform: scale(1.1);
}

.marquee-item.enlarged {
    z-index: 2;
}

.marquee-item.enlarged .algorithm-card {
    transform: scale(1.1);
    transition: transform 0.3s;
    box-shadow: 0 0 .0625rem #fff;
}

.marquee-item .algorithm-card {
    width: 100%;
}

.algorithm-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    height: 100%;
    position: relative;
    width: 4.5rem;
    height: 2.7rem;

    .imgType {
        position: absolute;
        left: .125rem;
        top: .075rem;
        font-size: .15rem;
        color: #4ab4ff;
        padding: .075rem;
        border: .0125rem solid #4ab4ff;
        border-radius: .0625rem;
        background: rgba(87, 88, 89, .6);
    }

    .imgName {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: .55rem;
        line-height: .55rem;
        font-size: .15rem;
        text-align: center;
        background: rgba(0, 0, 0, .45);
        color: #fff;
    }
}

.algorithm-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.selected-algorithm {
    border-color: var(--q-primary);
    box-shadow: 0 0 10px rgba(var(--q-primary-rgb), 0.3);
}

.chart-container {
    width: 100%;
    height: 2.875rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

:deep(.el-step__icon) {
    width: .45rem;
    height: .45rem;
    font-size: .225rem;
}

:deep(.el-step.is-horizontal .el-step__line) {
    top: 50%;
}

.content .top .right .custom-steps :deep(.el-step__title) {

    &.is-success,
    &.is-finish {
        color: #67c23a !important;
    }
}

.next {
    display: flex;
    gap: .25rem;
    width: 10%;
    position: absolute;
    right:0;
    top:-4vh;
    .prevBtn {
        margin-right: auto;
    }

    .nextBtn {
        margin-left: auto;
    }
}
</style>
