from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
import os
from reinforce_platform.settings import BASE_DIR


class ProjectInfoView(APIView):
    """
    返回项目信息
    """
    permission_classes = [permissions.AllowAny]
    def get(self, request):
        try:
            # 读取project_info.txt文件
            file_path = os.path.join(BASE_DIR, 'project_info.txt')
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析文件内容为字典
            project_info = {}
            for line in content.strip().split('\n'):
                if ':' in line:
                    key, value = line.split(':', 1)
                    project_info[key.strip().strip("'")] = value.strip().strip("'")

            # 返回项目信息
            return Response({"project_info": project_info}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
