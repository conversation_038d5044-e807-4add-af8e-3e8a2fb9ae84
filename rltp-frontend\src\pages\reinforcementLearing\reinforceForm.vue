<template>
  <div class="main-Form">
    <!-- 按钮区 -->
    <div class="Top">
      <TitleBtn ref="titleBtnRef"></TitleBtn>
      <q-btn class="doReturn" @click="returnToOverview">
        <img class="returnIcon" src="../../assets/images/icon_fh.png" alt="" />
        <div class="labelColor">返回模型概览</div>
      </q-btn>
    </div>

    <!-- 根据当前步骤显示对应表单 -->
    <keep-alive>
      <component
        :is="currentStepComponent"
        @next-step="handleNextStep"
        @prev-step="handlePrevStep"
      ></component>
    </keep-alive>
  </div>
</template>

<script setup>
import { defineAsyncComponent, ref, computed, onMounted, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import TitleBtn from "./components/TitleBtn.vue";
import { useReinforcementLearningStore } from "../../stores/reinforcementLearingStore";

// 异步加载拆分为独立chunk
const stepComponents = {
  1: defineAsyncComponent(() => import("./StepOne.vue")),
  2: defineAsyncComponent(() => import("./StepTwo.vue")),
  3: defineAsyncComponent(() => import("./StepThree.vue")),
  4: defineAsyncComponent(() => import("./StepFour.vue")),
  5: defineAsyncComponent(() => import("./StepFive.vue")),
};

const router = useRouter();
const route = useRoute();
const reinforcementStore = useReinforcementLearningStore();

// TitleBtn组件引用
const titleBtnRef = ref(null);

// 当前步骤计算属性，与TitleBtn同步
const currentStep = computed(() => {
  return titleBtnRef.value?.currentStep || 1;
});

// 当前步骤对应的组件
const currentStepComponent = computed(() => stepComponents[currentStep.value]);

// 处理下一步
const handleNextStep = () => {
  if (titleBtnRef.value) {
    titleBtnRef.value.nextStep();
    console.log("当前步骤:", titleBtnRef.value.currentStep);
  }
};

// 处理上一步
const handlePrevStep = () => {
  if (titleBtnRef.value) {
    titleBtnRef.value.prevStep();
    console.log("当前步骤:", titleBtnRef.value.currentStep);
  }
};

// 初始化页面状态
const initializeStepForm = async () => {
  const { task_id, current_step, step_configs } = route.query;
  
  if (task_id) {
    // 保存task_id到store
    reinforcementStore.setCurrentTaskId(task_id);
    console.log('设置当前任务ID:', task_id);
  }
  
  // 处理步骤配置数据
  if (step_configs) {
    try {
      const stepConfigsData = JSON.parse(step_configs);
      console.log('解析步骤配置数据:', stepConfigsData);
      
      // 动态处理各步骤配置数据并更新store
      Object.entries(stepConfigsData).forEach(([configKey, configData]) => {
        // 从配置键名中提取步骤号 (例如: step1_config -> 1)
        const stepMatch = configKey.match(/^step(\d+)_config$/);
        if (stepMatch && configData) {
          const stepNumber = parseInt(stepMatch[1]);
          updateStoreWithStepData(configData, stepNumber);
        }
      });
    } catch (error) {
      console.error('解析步骤配置数据失败:', error);
    }
  }
  
  if (current_step) {
    const stepNumber = parseInt(current_step);
    if (stepNumber >= 1 && stepNumber <= 5) {
      // 等待TitleBtn组件挂载完成后再设置步骤
      await nextTick();
      if (titleBtnRef.value) {
        titleBtnRef.value.goToStep(stepNumber);
        console.log('跳转到当前步骤:', stepNumber);
      }
    }
  }
};

// 步骤配置映射表 - 将API字段映射到store字段
const STEP_CONFIG_MAPPING = {
  1: {
    description: '交互环境',
    updateMethod: 'updateStepOneData',
    fieldMappings: {
      scenarios: 'scenarios',
    }
  },
  2: {
    description: '训练参数相关',
    updateMethod: 'updateStepTwoData',
    fieldMappings: {
      // 训练参数映射
      epochs: { target: 'params.epochs', transform: String },
      batch_size: { target: 'params.batchSize', transform: String },
      optimizer: { target: 'params.optimizer' },
      learning_rate: { target: 'params.learningRate', transform: String },
      warmup_steps: { target: 'params.warmupSteps', transform: String },
      log_interval: { target: 'params.logInterval', transform: String },
      eval_interval: { target: 'params.evalInterval', transform: String },
      // 资源配置映射
      npu_count: { target: 'resources.npuCount', transform: String },
      cpu_count: { target: 'resources.cpuCount', transform: String }
    },
    initStructure: () => ({ params: {}, resources: {} })
  },
  3: {
    description: '训练执行相关',
    updateMethod: 'updateStepThreeData',
    fieldMappings: {
      training_status: 'trainingStatus',
      current_epoch: 'currentEpoch',
      total_epochs: 'totalEpochs',
      training_progress: 'trainingProgress'
    }
  }
};

// 设置嵌套对象属性的工具函数
const setNestedProperty = (obj, path, value) => {
  const keys = path.split('.');
  let current = obj;
  
  // 创建中间对象
  for (let i = 0; i < keys.length - 1; i++) {
    if (!(keys[i] in current)) {
      current[keys[i]] = {};
    }
    current = current[keys[i]];
  }
  
  // 设置最终值
  current[keys[keys.length - 1]] = value;
};

// 根据步骤数据更新store中的状态 - 优化版本
const updateStoreWithStepData = (stepData, stepNumber) => {
    if (!stepData) return
    
    const stepConfig = STEP_CONFIG_MAPPING[stepNumber]
    if (!stepConfig) {
        console.warn(`未找到步骤${stepNumber}的配置映射`)
        return
    }
    
    console.log(`开始更新第${stepNumber}步store数据 (${stepConfig.description}):`, stepData)
    
    // 初始化更新对象
    const updateData = stepConfig.initStructure ? stepConfig.initStructure() : {}
    let hasUpdates = false
    
    // 遍历字段映射并转换数据
    Object.entries(stepConfig.fieldMappings).forEach(([apiField, mapping]) => {
        if (stepData[apiField] !== undefined) {
            let targetField, transformFn
            
            // 处理不同类型的映射配置
            if (typeof mapping === 'string') {
                // 简单字符串映射
                targetField = mapping
                transformFn = (value) => value
            } else if (typeof mapping === 'object' && mapping.target) {
                // 对象映射配置
                targetField = mapping.target
                transformFn = mapping.transform || ((value) => value)
            } else {
                console.warn(`无效的映射配置: ${apiField} -> ${mapping}`)
                return
            }
            
            // 转换并设置值
            const transformedValue = transformFn(stepData[apiField])
            
            if (targetField.includes('.')) {
                // 处理嵌套属性
                setNestedProperty(updateData, targetField, transformedValue)
            } else {
                // 处理平级属性
                updateData[targetField] = transformedValue
            }
            
            hasUpdates = true
        }
    })
    
    // 如果有更新数据，则调用对应的store更新方法
    if (hasUpdates) {
        const updateMethod = stepConfig.updateMethod
        if (reinforcementStore[updateMethod]) {
          reinforcementStore[updateMethod](updateData)
            console.log(`更新第${stepNumber}步数据:`, updateData)
        } else {
            console.error(`Store中未找到更新方法: ${updateMethod}`)
        }
    } else {
        console.log(`第${stepNumber}步没有需要更新的数据`)
    }
}

// 返回概览页面
function returnToOverview() {
  router.push("/ai-model/reinforcementStudy"); //跳转到强化学习入口页
}

// 组件挂载时初始化
onMounted(async () => {
  await initializeStepForm();
});
</script>

<style lang="scss" scoped>
.main-Form {
  background-color: #131520;
  height: calc(100vh - 1rem); // 减去顶部padding和margin
  display: flex;
  flex-direction: column;
}

.Top {
  margin-top: 1rem;
  margin-bottom: 0.375rem;
  position: relative;
  .doReturn {
    position: absolute;
    left: 0;
    top: 2.5vh;
    display: flex;
    .returnIcon {
      width: 0.375rem;
      height: 0.375rem;
      margin-right: 0.125rem;
    }
  }
}
</style>
