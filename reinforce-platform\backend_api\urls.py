from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON>, DefaultRouter
from backend_api.views.overview import OverViewAPIView, DownloadsAPIView
from backend_api.views.storage import StorageDownloadView, StorageListView, StorageUploadView, StorageCreateDirView, StorageDeleteView
from backend_api.views.task import TaskResumeView, TaskSuspendingView, TaskViewSets, TaskLogView
from backend_api.views.task import TaskReleaseView
from backend_api.views.scenario import ScenarioViewSets, ScenarioDownloadFileView, ScenarioCheckFileView
from backend_api.views.algorithm import AlgorithmViewSets
from backend_api.views.model import ModelViewSets
from backend_api.views.model import ModelDeductionView
from backend_api.views.simulator import SimulatorViewSets
from backend_api.views.deduction import DeductionViewSets
from backend_api.views.evaluation.evaluation import EvaluationViewSets
from backend_api.views.evaluation.evaluation_test import EvaluationTestViewSets
from backend_api.views.policy.policy import PolicyViewSets
from backend_api.views.policy.policy_evaluation import PolicyEvaluationViewSets
from backend_api.views.environment import EnvironmentViewSet
from backend_api.views.dataset import DatasetViewSets
from backend_api.views.emulator import EmulatorViewSets, EmulatorStatsView
from backend_api.views import training
from backend_api.views.training import (
    TrainingStartView,
    TrainingMetricsView,
    TrainingMetricsStreamView,
    TrainingCancelView,
    TrainingPauseView,
    TrainingResumeView,
    TrainingLogsView,
    TrainingLogStreamView,
    DLTrainingConfigSaveView,
    DLTrainingConfigImportView,
    DLTrainingConfigListView,
    DLTrainingConfigDeleteView,
    DLTrainingConfigExportView
)
from backend_api.views.training_model import (
    TrainingModelListView,
    TrainingModelDetailView,
    TrainingModelCreateView,
    # ModelConversionView,
    # ModelConversionStatusView,
    ModelInferenceView,
    ModelInferenceStatusView,
    ModelConversionLogView,
    ModelInferenceLogView,
    TrainingModelViewSet
)
from backend_api.views.rl_training import (
    RLTrainingStartView,
    RLTrainingStopView,
    RLTrainingPauseView,
    RLTrainingResumeView,
    RLTrainingStatusView,
    RLTrainingMetricsView,
    RLTrainingMetricsStreamView,
    RLTrainingResourcesView,
    RLTrainingModelInfoView,
    RLTrainingModelDownloadView,
    RLTrainingModelListView,
    RLTrainingLogStreamView,
    RLTrainingConfigSaveView,
    RLTrainingConfigImportView,
    RLTrainingConfigListView,
    RLTrainingTaskListView,
    RLTrainingConfigDeleteView,
    RLTrainingConfigExportView
)
from backend_api.views.training_workflow import (
    WorkflowCreateView,
    WorkflowStepSaveView,
    WorkflowDataAnalysisView,
    WorkflowTrainingSubmitView,
    WorkflowTrainingControlView,
    WorkflowListView,
    WorkflowJumpToStepView,
    WorkflowTrainingMetricsUpdateView,
    WorkflowStepCompleteView
)
from backend_api.views.project import ProjectInfoView


router = SimpleRouter()
# 可以通过router默认路由注册资源
router.register('tasks', TaskViewSets)
router.register('scenarios', ScenarioViewSets)
router.register('algorithms', AlgorithmViewSets)
router.register('models', ModelViewSets)
router.register('simulators', SimulatorViewSets)
router.register('deductions', DeductionViewSets)
router.register('evaluations', EvaluationViewSets)
router.register('evaluation_tests', EvaluationTestViewSets)
router.register('policies', PolicyViewSets)
router.register('policy_evaluations', PolicyEvaluationViewSets)
router.register('environments', EnvironmentViewSet)
router.register('datasets', DatasetViewSets)
router.register('emulators', EmulatorViewSets)
router.register('training-models', TrainingModelViewSet)


urlpatterns = [
    # 仿真器相关路由 - 放在最前面以确保优先匹配
    path('emulators/stats/', EmulatorStatsView.as_view(), name='emulator-stats'),
    # 任务相关路由
    path('tasks/release/', TaskReleaseView.as_view(), name='task_release'),
    path('tasks/suspending/', TaskSuspendingView.as_view(), name='task_suspending'),
    path('tasks/resume/', TaskResumeView.as_view(), name='task_resume'),
    path('tasks/log/', TaskLogView.as_view(), name='task_log'),
    path('scenarios/download_file', ScenarioDownloadFileView.as_view(), name='scenario_download_file'),
    path('scenarios/check_file', ScenarioCheckFileView.as_view(), name='scenario_check_file'),
    path('models/deduction/', ModelDeductionView.as_view(), name='model_deduction'),
    path('storages/list/', StorageListView.as_view(), name='storage_list'),
    path('storages/upload/', StorageUploadView.as_view(), name='storage_upload'),
    path('storages/createdir/', StorageCreateDirView.as_view(), name='storage_createdir'),
    path('storages/delete/', StorageDeleteView.as_view(), name='storage_delete'),
    path('storages/download/', StorageDownloadView.as_view(), name='storage_download'),
    path('overview/', OverViewAPIView.as_view(), name='overview'),
    path('downloads/', DownloadsAPIView.as_view(), name='downloads'),

    # 深度学习训练相关接口
    path('training/start', TrainingStartView.as_view(), name='start_training'),
    path('training/<int:training_id>/metrics', TrainingMetricsView.as_view(), name='get_training_metrics'),
    path('training/<int:training_id>/metrics-stream', TrainingMetricsStreamView.as_view(), name='training_metrics_stream'),
    path('training/<int:training_id>/cancel', TrainingCancelView.as_view(), name='cancel_training'),
    path('training/<int:training_id>/pause', TrainingPauseView.as_view(), name='pause_training'),
    path('training/<int:training_id>/resume', TrainingResumeView.as_view(), name='resume_training'),
    path('training/<int:training_id>/logs', TrainingLogsView.as_view(), name='training_logs'),
    path('training/<int:training_id>/log-stream', TrainingLogStreamView.as_view(), name='training_log_stream'),
   
    # 深度学习配置管理
    path('training/dl/config', DLTrainingConfigSaveView.as_view(), name='dl_config_save'),
    path('training/dl/config/import', DLTrainingConfigImportView.as_view(), name='dl_config_import'),
    path('training/dl/config/list', DLTrainingConfigListView.as_view(), name='dl_config_list'),
    path('training/dl/config/<str:config_id>/delete', DLTrainingConfigDeleteView.as_view(), name='dl_config_delete'),
    path('training/dl/config/<str:config_id>/export', DLTrainingConfigExportView.as_view(), name='dl_config_export'),

    # 训练模型管理相关接口
    path('training/models', TrainingModelListView.as_view(), name='training_model_list'),
    path('training/models/create', TrainingModelCreateView.as_view(), name='training_model_create'),
    path('training/models/<int:model_id>', TrainingModelDetailView.as_view(), name='training_model_detail'),
    # 模型转换接口已被移除
    # path('training/models/convert', ModelConversionView.as_view(), name='model_conversion'),
    # path('training/models/convert/<int:conversion_log_id>/status', ModelConversionStatusView.as_view(), name='model_conversion_status'),
    path('training/models/inference', ModelInferenceView.as_view(), name='model_inference'),
    path('training/models/inference/<int:inference_log_id>/status', ModelInferenceStatusView.as_view(), name='model_inference_status'),
    path('training/models/<int:model_id>/conversion-logs', ModelConversionLogView.as_view(), name='model_conversion_logs'),
    path('training/models/<int:model_id>/inference-logs', ModelInferenceLogView.as_view(), name='model_inference_logs'),
    path('training/conversion-logs', ModelConversionLogView.as_view(), name='all_conversion_logs'),
    path('training/inference-logs', ModelInferenceLogView.as_view(), name='all_inference_logs'),

    # 强化学习训练相关接口
    path('training/rl/start', RLTrainingStartView.as_view(), name='rl_training_start'),
    path('training/rl/<str:training_id>/stop', RLTrainingStopView.as_view(), name='rl_training_stop'),
    path('training/rl/<str:training_id>/pause', RLTrainingPauseView.as_view(), name='rl_training_pause'),
    path('training/rl/<str:training_id>/resume', RLTrainingResumeView.as_view(), name='rl_training_resume'),
    path('training/rl/<str:training_id>/status', RLTrainingStatusView.as_view(), name='rl_training_status'),
    path('training/rl/<str:training_id>/metrics', RLTrainingMetricsView.as_view(), name='rl_training_metrics'),
    path('training/rl/<str:training_id>/metrics-stream', RLTrainingMetricsStreamView.as_view(), name='rl_training_metrics_stream'),
    path('training/rl/<str:training_id>/resources', RLTrainingResourcesView.as_view(), name='rl_training_resources'),
    path('training/rl/<str:training_id>/model/info', RLTrainingModelInfoView.as_view(), name='rl_training_model_info'),
    path('training/rl/<str:training_id>/model/download', RLTrainingModelDownloadView.as_view(), name='rl_training_model_download'),
    path('training/rl/<str:training_id>/models', RLTrainingModelListView.as_view(), name='rl_training_model_list'),
    path('training/rl/<str:training_id>/log-stream', RLTrainingLogStreamView.as_view(), name='rl_training_log_stream'),
                    
    # 强化学习配置管理
    path('training/rl/config', RLTrainingConfigSaveView.as_view(), name='rl_config_save'),
    path('training/rl/config/import', RLTrainingConfigImportView.as_view(), name='rl_config_import'),
    path('training/rl/config/list', RLTrainingConfigListView.as_view(), name='rl_config_list'),
    path('training/rl/config/<str:config_id>/delete', RLTrainingConfigDeleteView.as_view(), name='rl_config_delete'),
    path('training/rl/config/<str:config_id>/export', RLTrainingConfigExportView.as_view(), name='rl_config_export'),
   
    # 强化学习任务管理
    path('training/rl/tasks', RLTrainingTaskListView.as_view(), name='rl_task_list'),

    # 统一训练工作流API
    path('workflows/create/', WorkflowCreateView.as_view(), name='workflow_create'),
    path('workflows/step/save/', WorkflowStepSaveView.as_view(), name='workflow_step_save'),
    path('workflows/data/analysis/', WorkflowDataAnalysisView.as_view(), name='workflow_data_analysis'),
    path('workflows/training/submit/', WorkflowTrainingSubmitView.as_view(), name='workflow_training_submit'),
    path('workflows/training/control/', WorkflowTrainingControlView.as_view(), name='workflow_training_control'),
    path('workflows/step/complete/', WorkflowStepCompleteView.as_view(), name='workflow_step_complete'),
    path('workflows/list/', WorkflowListView.as_view(), name='workflow_list'),
    path('workflows/jump/', WorkflowJumpToStepView.as_view(), name='workflow_jump_to_step'),
    path('workflows/metrics/update/', WorkflowTrainingMetricsUpdateView.as_view(), name='workflow_metrics_update'),

    # 版本信息接口
    path('project/version/', ProjectInfoView.as_view(), name='get_project'),


]

# 添加router的URL到urlpatterns
urlpatterns += router.urls
