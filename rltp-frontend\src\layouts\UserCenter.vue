<template>
  <div class="flex">
    <div class="right-container-flex">

      <q-btn-dropdown icon="folder" class="dropDown" label="综合管理" no-caps flat text-color="black">

        <q-item clickable v-close-popup @click="goToUserManagement">
          <q-item-section>
            <div class="item">
              <div style="font-size:.175rem;">用户管理</div>
            </div>
          </q-item-section>
        </q-item>
        <q-list>
          <q-item clickable v-close-popup @click="goToServerManagement">
            <q-item-section>
              <div class="item">
                <div style="font-size:.175rem;">服务器运维管理</div>
              </div>
            </q-item-section>
          </q-item>
        </q-list>
      </q-btn-dropdown>

      <q-btn>
        <div style="display: flex;justify-content: center;align-items: center;">
          <div class="avatar-container">
            <img :src="userAvatarUrl" class="user-avatar" />
          </div>


          <q-btn-dropdown class="dropDown noHover" :label="localStorage.getItem('username')" unelevated color="cyan"
            no-caps>
            <q-list>
              <q-item clickable v-close-popup @click="logout">
                <q-item-section>
                  <div class="item" >
                    <img class="logout-icon" src="../assets/images/logout.png" alt="">
                    <div class="q-ml-xs" style="font-size:.175rem;">退出</div>
                  </div>
                </q-item-section>
              </q-item>
            </q-list>
          </q-btn-dropdown>
        </div>
      </q-btn>

      <q-btn>
        <img class="bell" src="../assets/images/bell.png" alt="">
        <q-badge color="red" rounded floating>3</q-badge>
      </q-btn>

    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { usePlugin } from 'composables/plugin.js'
import SvgLogout from 'assets/images/svg/logout.svg'
import { ref, computed } from 'vue'
import { useUserStore } from '../stores/userStore.js'
import { storeToRefs } from 'pinia'

const router = useRouter()
const { dialog, localStorage } = usePlugin()

// 获取用户头像
const userStore = useUserStore()
// 直接使用store中的userAvatarUrl计算属性，它已经处理了默认头像逻辑
const { userAvatarUrl } = storeToRefs(userStore)



function goToServerManagement() {
  router.push('/system/server')
}

function goToUserManagement() {
  router.push('/system/users')
}

function logout() {
  dialog('确定要退出登录吗？').onOk(() => {
    localStorage.remove('token')
    localStorage.remove('user')
    localStorage.remove('username')
    // 清除头像
    userStore.changeUserAvatar('')
    router.push('/login')
  })
}
</script>

<style lang="scss" scoped>
.right-container-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 4.6125rem;
}

.q-chip {
  background: none !important;
}

.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.bell {
  width: .3rem;
  height: .3rem;
}

.logout-icon {
  width: .3rem;
  height: .3rem;
}

.example-showcase .el-dropdown-link {
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
}

.bg-cyan {
  background: none !important;
}

.dropDown {
  padding: 0 !important;

  :deep(.q-btn-dropdown__arrow) {
    //箭头
    margin-left: 0px !important;
  }
}

.noHover {
  :deep(.q-focus-helper) {
    opacity: 0 !important;
  }
}

// 退出框
.q-dialog-plugin {}


.q-btn:before {
  box-shadow: none !important;
}


:deep(.q-btn) {
  color: white !important;
  font-size: .2rem !important;
  ;
}

.q-badge {
  font-size: .15rem !important;
  line-height: .15rem !important;
  padding: .025rem .075rem;
}

.avatar-container {
  height: .4rem;
  margin-right: .0625rem;
}

.user-avatar {
  width: 0.4rem;
  height: 0.4rem;
  border-radius: 50%;
  object-fit: cover;
}
</style>
