<!--
 * @Author: Szc
 * @Date: 2025-06-26 15:00:49
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-29 10:11:38
 * @Description: 
-->

<template>
  <div class="q-pa-md mt90 custom">
    <div class="row q-col-gutter-md">
      <div class="col-7">
        <div class="showBorder q-mb-md">
          <q-card-section class="h-100">
            <div class="labelColor">
              <span class="title"> 时敏目标识别模型：</span>
              <span class="content">
                接入汇集侦察传感器（无人机、卫星）的红外、可见光以及SAR图像，从图像中检测识别海马斯、雷霆-2000等时敏目标。
              </span>
            </div>
          </q-card-section>
        </div>

        <div class="marquee-container q-mb-md">
          <div class="labelColor font16 algotitle botLine">模型列表</div>
          <div class="marquee-content" ref="marqueeContent" :style="{ transform: `translateX(-${translateX}px)` }">
            <div class="quard-item marquee-item" v-for="(algo, index) in displayedItems" :key="index"
              @click="toggleImageSize(algo)" :class="{ enlarged: selectedItem === algo || isCenterImage(index) }">
              <q-card class="algorithm-card" :class="{ 'selected-algorithm': currentAlgorithm === algo.id }"
                @click="selectAlgorithm(algo)">
                <q-img :src="algo.image_url || defaultBackground" style="height: 100%">
                  <template v-slot:error>
                    <div class="absolute-full flex flex-center bg-grey-3 text-grey-7">
                      <q-icon name="image" size="2em" />
                    </div>
                  </template>
                </q-img>
                <div class="imgType">{{ algo.version }}</div>
                <div class="imgName">{{ algo.name }}</div>
              </q-card>
            </div>
          </div>
        </div>

        <div class="training-section q-mb-md">
          <q-btn class="training-btn cus-bg" @click="startTraining" no-caps>
            <div class="training-btn-content">
              <div class="labelColor textSize">开始训练</div>
              <img src="../../assets/images/entry.png" alt="" class="entry-icon" />
            </div>
          </q-btn>
        </div>

        <div class="showBorder radar" style="height: 41vh;">
          <q-card-section style="padding-left:0 !important;padding-right:0 !important;padding-bottom: 0 !important;">
            <div class="mb10 labelColor font16 botLine">模型评估</div>
            <div class="evaluation-content">
              <!-- 雷达图：评估指标 -->
              <div ref="radarChart" class="radar-chart-container"></div>
            </div>
          </q-card-section>
        </div>
      </div>

      <div class="col-5">
        <!-- 训练任务卡片列表 -->
        <div 
          class="task-cards-container"
          ref="taskListContainer"
          @scroll="handleScroll"
        >
          <!-- 加载状态 -->
          <div v-if="loadingTasks" class="loading-container">
            <q-spinner color="primary" size="40px" />
            <div class="labelColor">加载任务列表中...</div>
          </div>
          
          <!-- 任务列表 -->
          <div v-else-if="trainTasks.length > 0">
            <div 
              v-for="task in trainTasks" 
              :key="task.id" 
              class="task-card q-mb-md"
              @click="handleTaskClick(task)"
            >
              <q-card class="task-card-inner showBorder">
                <q-card-section class="task-card-content">
                  <div class="task-header">
                    <div class="task-name labelColor font16">{{ task.name }}</div>
                  </div>

                  <div class="task-info">
                    <div class="info-item">
                      <span class="info-label">资源配置:</span>
                      <span class="info-value">{{
                        task.actor_num * task.actor_per_cpu +
                        task.learner_num * task.learner_per_cpu || "未知"
                      }}
                        CPU</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">训练时长:</span>
                      <span class="info-value">{{
                        convertSecondsToHMS(task.running_time)
                      }}</span>
                    </div>
                  </div>

                  <div class="task-actions">
                    <div class="status-display">
                      <span class="status-label-text">训练状态:</span>
                      <span class="status-value" :class="getStatusColor(task.training_status)">
                        {{ task.training_status_display || task.status_display || task.training_status }}
                      </span>
                    </div>
                  </div>
                </q-card-section>
              </q-card>
            </div>
            
            <!-- 加载更多提示 -->
            <div v-if="isLoadingMore" class="loading-more">
              <q-spinner color="primary" size="20px" />
              <span class="labelColor">加载更多...</span>
            </div>
            
            <!-- 没有更多数据提示 -->
            <div v-else-if="!hasMore && trainTasks.length > 0" class="no-more-data">
              <span class="labelColor">没有更多数据了</span>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-else class="empty-state showBorder">
            <q-icon name="inbox" size="60px" class="empty-icon" />
            <div class="labelColor">暂无深度学习任务</div>
            <div class="labelColor font14">点击"开始训练"创建新任务</div>
          </div>
        </div>
      </div>
    </div>

    
    <q-btn class="doReturn" @click="goHome">
      <img class="returnIcon" src="../../assets/images/icon_fh.png" alt="">
      <div class="labelColor">返回首页</div>
    </q-btn>

  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed, nextTick } from "vue";
import { api } from "boot/axios";
import { usePlugin } from "composables/plugin.js";
import { useRouter, useRoute } from "vue-router";
import { useModelFormStore } from "../../stores/modelFormStore";

import { convertSecondsToHMS } from "assets/utils";
import { createWorkFlow, getWorkflowList, jumpToStep } from "src/request/workFlow";
import defaultBackground from "assets/images/smart_model_backgroud.png";

import jun1 from "assets/images/jun1.png";
import jun2 from "assets/images/jun2.png";
import jun3 from "assets/images/jun3.png";
import jun4 from "assets/images/jun4.png";
import jun5 from "assets/images/jun5.png";
import jun6 from "assets/images/jun6.png";
import jun7 from "assets/images/jun7.png";
import jun8 from "assets/images/jun8.png";
import jun9 from "assets/images/jun9.png";
import jun10 from "assets/images/jun10.png";
import * as echarts from "echarts";

const { notify } = usePlugin();
const router = useRouter();
const route = useRoute();
const modelFormStore = useModelFormStore();

const goHome = ()=>{
  router.replace('/')
}

const radarChart = ref(null);
let radarChartInstance = null;

// 算法列表数据
const algorithms = ref([]);
const currentAlgorithm = ref(null);


// 任务列表数据
const trainTasks = ref([]);
const loadingTasks = ref(false);

// 分页相关状态
const currentPage = ref(1);
const pageSize = ref(10);
const hasMore = ref(true);
const isLoadingMore = ref(false);
const taskListContainer = ref(null);


const marqueeContent = ref(null);
const translateX = ref(0);
const speed = 3;
let intervalId = null;
const selectedItem = ref(null);
const isClick = ref(false);

const displayedItems = computed(() => [...algorithms.value, ...algorithms.value]);

// 固定的卡片尺寸配置（基于CSS中的rem值）
const CARD_CONFIG = {
  widthRem: 3.125,
  marginRightRem: 0.5,
};

// 获取根元素字体大小
const getRootFontSize = () => {
  return parseFloat(getComputedStyle(document.documentElement).fontSize);
};

let containerWidthCache = 0;
const getContainerWidth = () => {
  if (containerWidthCache === 0) {
    const container = document.querySelector(".marquee-container");
    containerWidthCache = container ? container.offsetWidth : 800;
  }
  return containerWidthCache;
};

const resetContainerWidthCache = () => {
  containerWidthCache = 0;
};

// 缓存计算结果，减少重复计算
let cachedRootFontSize = 0;
let cachedItemWidth = 0;

const getCachedItemWidth = () => {
  if (cachedItemWidth === 0 || cachedRootFontSize === 0) {
    //说明重置了 或者 第一次
    cachedRootFontSize = getRootFontSize();
    const cardWidth = CARD_CONFIG.widthRem * cachedRootFontSize;
    const margin = CARD_CONFIG.marginRightRem * cachedRootFontSize;
    cachedItemWidth = cardWidth + margin;
  }
  return cachedItemWidth;
};

const resetItemWidthCache = () => {
  cachedRootFontSize = 0;
  cachedItemWidth = 0;
};

const centerItemIndex = computed(() => {
  const itemsLen = algorithms.value.length;
  if (itemsLen === 0) return 0;

  const containerWidth = getContainerWidth();
  const itemWidth = getCachedItemWidth();

  return Math.floor((translateX.value + containerWidth / 2) / itemWidth) % itemsLen;
});

// 跑马灯状态控制
const isMarqueeActive = ref(true);

const startMarquee = () => {
  clearInterval(intervalId);
  intervalId = setInterval(() => {
    if (isMarqueeActive.value && !document.hidden) {
      translateX.value += speed;
      if (
        marqueeContent.value &&
        translateX.value >= marqueeContent.value.scrollWidth / 2
      ) {
        translateX.value = 0;
      }
    }
  }, 20);
};

const stopMarquee = () => {
  clearInterval(intervalId);
  intervalId = null; // 志超专属防御性编程
};

const pauseMarquee = () => {
  isMarqueeActive.value = false; //暂停
};

const resumeMarquee = () => {
  isMarqueeActive.value = true; // 恢复
};

const toggleImageSize = (item) => {
  console.log("点击了", item, selectedItem.value);

  if (selectedItem.value === item) {
    console.log("第二次点击嘿嘿"); //再次
    isClick.value = false;
    selectedItem.value = null;
    startMarquee();
  } else {
    console.log("第一次点击哈哈"); //首次
    isClick.value = true;
    selectedItem.value = item;
    stopMarquee(); // 使用封装的停止方法
  }
};

const isCenterImage = (index) => {
  const itemsLen = algorithms.value.length;
  return (
    (index === centerItemIndex.value || index === centerItemIndex.value + itemsLen) &&
    !selectedItem.value
  );
};

// 获取任务列表（支持分页和追加数据）
const fetchTaskList = async (isLoadMore = false) => {
  if (isLoadMore) {
    isLoadingMore.value = true;
  } else {
    loadingTasks.value = true;
    currentPage.value = 1;
    trainTasks.value = [];
    hasMore.value = true;
  }

  try {
    const response = await getWorkflowList({
      page: currentPage.value,
      page_size: pageSize.value,
      training_type: "deep_learning" // 只获取深度学习任务
    });

    console.log("API返回的原始数据:", response);

    const workflows = response.data.workflows || [];

    if (workflows.length > 0) {
      // 将API数据映射为组件需要的格式 - 只映射API返回的字段
      const newTasks = workflows.map(workflow => ({
        // === API返回的实际字段 ===
        id: workflow.task_id, // 兼容组件使用的id字段
        task_id: workflow.task_id,
        name: workflow.name,
        task_type: workflow.task_type,
        training_type: workflow.training_type,
        training_type_display: workflow.training_type_display,
        
        // 状态信息
        status: workflow.status,
        status_display: workflow.status_display,
        training_status: workflow.training_status,
        training_status_display: workflow.training_status_display,
        
        // 时间信息
        created_at: workflow.created_at,
        updated_at: workflow.updated_at,
        training_started_at: workflow.training_started_at,
        
        // 步骤和进度信息
        current_step: workflow.current_step,
        next_step_number: workflow.next_step_number,
        step_name: workflow.step_name,
        progress_percentage: workflow.progress_percentage,
        can_proceed: workflow.can_proceed,
        
        // === 组件需要的计算字段 ===
        running_time: calculateRunningTime(workflow),
        
        // === 组件需要的默认字段（用于显示资源配置）===
        actor_num: 4,
        actor_per_cpu: 2,
        learner_num: 2,
        learner_per_cpu: 4
      }));

      if (isLoadMore) {
        // 追加数据
        trainTasks.value.push(...newTasks);
      } else {
        // 首次加载，替换数据
        trainTasks.value = newTasks;
      }

      // 判断是否还有更多数据
      hasMore.value = newTasks.length === pageSize.value;
      
      console.log("获取到的深度学习任务列表:", {
        isLoadMore,
        currentPage: currentPage.value,
        newTasksCount: newTasks.length,
        totalTasksCount: trainTasks.value.length,
        hasMore: hasMore.value
      });
    } else {
      console.log("未找到workflows数据");
      // 如果API返回成功但没有数据，显示空状态
      if (!isLoadMore) {
        trainTasks.value = [];
      }
      hasMore.value = false;
    }
  } catch (error) {
    console.error("获取任务列表失败:", error);
    
    // 显示错误提示
    notify("获取任务列表失败: " + error.message, "negative");
    
    // 请求失败时，不追加数据，只在首次加载失败时清空列表
    if (!isLoadMore) {
      trainTasks.value = [];
    }
    
    // 标记没有更多数据
    hasMore.value = false;
  } finally {
    loadingTasks.value = false;
    isLoadingMore.value = false;
  }
};

// 加载更多数据
const loadMore = async () => {
  if (!hasMore.value || isLoadingMore.value) return;
  
  currentPage.value += 1;
  await fetchTaskList(true);
};

// 滚动事件处理
const handleScroll = (e) => {
  const { scrollTop, scrollHeight, clientHeight } = e.target;
  
  // 当滚动到底部附近时（距离底部100px以内），触发加载更多
  if (scrollHeight - scrollTop - clientHeight < 100 && hasMore.value && !isLoadingMore.value) {
    loadMore();
  }
};

// 根据训练状态返回对应的颜色类
const getStatusColor = (trainingStatus) => {
  const statusColorMap = {
    'not_started': 'text-grey-5',
    'running': 'text-green',
    'paused': 'text-orange',
    'stopped': 'text-red',
    'completed': 'text-blue',
    'failed': 'text-red'
  };
  
  return statusColorMap[trainingStatus] || 'text-grey-5';
};

// 计算运行时间 - 基于API返回的实际字段
const calculateRunningTime = (workflow) => {
  // 如果有训练开始时间且训练状态是运行中，使用训练开始时间计算
  if (workflow.training_started_at && workflow.training_status === 'running') {
    const startTime = new Date(workflow.training_started_at);
    const now = new Date();
    return Math.floor((now - startTime) / 1000); // 返回秒数
  }
  // 如果训练状态是运行中但没有训练开始时间，使用创建时间
  else if (workflow.training_status === 'running' && workflow.created_at) {
    const startTime = new Date(workflow.created_at);
    const now = new Date();
    return Math.floor((now - startTime) / 1000); // 返回秒数
  }
  // 其他情况返回0（如：not_started、draft等状态）
  return 0;
};

// 步骤配置字段映射
const STEP_CONFIG_FIELDS = ['step1_config', 'step2_config', 'step3_config'];

// 处理任务卡片点击事件
const handleTaskClick = async (task) => {
  console.log("点击任务:", task);
  try {
    const response = await jumpToStep({ task_id: task.task_id });
    
    if (response && response.success) {
      // 保存 task_id 到 store
      modelFormStore.setCurrentTaskId(task.task_id);
      
      const currentStep = response.data.current_step;
      console.log("jumpToStep API 响应数据:", response.data);
      console.log("当前步骤:", currentStep);
      
      // 动态构造步骤配置数据对象
      const stepConfigs = {};
      STEP_CONFIG_FIELDS.forEach(field => {
        const configData = response.data[field];
        if (configData) {
          stepConfigs[field] = configData;
          console.log(`${field}:`, configData);
        }
      });
      
      // 根据当前步骤跳转到对应页面，同时传递步骤配置数据
      router.push({
        path: "/ai-model/startTrain",
        query: {
          task_id: task.task_id,
          current_step: currentStep,
          // 将步骤配置数据序列化后传递
          step_configs: JSON.stringify(stepConfigs)
        }
      });
    } else {
      notify("跳转到指定步骤失败", "negative");
    }
  } catch (error) {
    console.error("跳转到指定步骤失败:", error);
    notify("跳转到指定步骤失败", "negative");
  }
};

// 获取算法列表
const fetchAlgorithms = async () => {
  try {
    const response = await api.get("/backend/algorithms");
    if (response && response.results) {
      algorithms.value = response.results;
    } else {
      throw new Error("获取数据格式错误");
    }
  } catch (error) {
    console.error("获取算法列表失败", error);
    // 设置演示数据
    algorithms.value = [
      { id: "v1", name: "SSD算法", version: "v1.0.2", image_url: defaultBackground },
      {
        id: "v2",
        name: "YOLO算法",
        version: "v2.1.3",
        image_url: "assets/images/version2.jpg",
      },
      {
        id: "v3",
        name: "R-CNN算法",
        version: "v1.5.0",
        image_url: "assets/images/version3.jpg",
      },
      {
        id: "v4",
        name: "BERT模型",
        version: "v2.0.1",
        image_url: "assets/images/version4.jpg",
      },
      {
        id: "v4",
        name: "BERT模型",
        version: "v2.0.1",
        image_url: "assets/images/version4.jpg",
      },
      {
        id: "v4",
        name: "BERT模型",
        version: "v2.0.1",
        image_url: "assets/images/version4.jpg",
      },
      {
        id: "v4",
        name: "BERT模型",
        version: "v2.0.1",
        image_url: "assets/images/version4.jpg",
      },
      {
        id: "v4",
        name: "BERT模型",
        version: "v2.0.1",
        image_url: "assets/images/version4.jpg",
      },
      {
        id: "v4",
        name: "BERT模型",
        version: "v2.0.1",
        image_url: "assets/images/version4.jpg",
      },
      {
        id: "v4",
        name: "BERT模型",
        version: "v2.0.1",
        image_url: "assets/images/version4.jpg",
      },
    ];
    console.log("catch >>>", algorithms.value);
  } finally {
    algorithms.value = [
      { id: "v1", name: "YOLOv5(红外)", version: "v1.1", image_url: jun1 },
      { id: "v2", name: "YOLOv5(SAR)", version: "v1.1", image_url: jun2 },
      { id: "v3", name: "YOLOv5(可见光)", version: "v1.1", image_url: jun3 },
      { id: "v4", name: "YOLOv8(红外)", version: "v1.1", image_url: jun4 },
      { id: "v5", name: "YOLOv8(可见光)", version: "v1.1", image_url: jun5 },
      { id: "v6", name: "YOLOv8(SAR)", version: "v1.1", image_url: jun6 },
      { id: "v7", name: "YoloV8(红外)", version: "v1.2", image_url: jun7 },
      { id: "v8", name: "YoloV8(可见光)", version: "v1.2", image_url: jun8 },
      { id: "v9", name: "YoloV8(可见光)", version: "v1.3", image_url: jun9 },
      { id: "v10", name: "YoloV8(可见光)", version: "v1.4", image_url: jun10 },
    ];
  }
};

// 初始化图表
const initCharts = () => {
  // 初始化雷达图
  if (radarChart.value) {
    radarChartInstance = echarts.init(radarChart.value);
    const radarOption = {
      title: {
        text: "",
      },
      tooltip: {
        axisPointer: {
          type: "shadow",
        },
        textStyle: {
          fontSize: window.screen.width > 1536 ? "20" : "14", // 文字大小设为14px
          color: "#333", // 文字颜色设为#333
        },
      },
      legend: {
        show: false,
        data: ["模型性能"],
        textStyle: {
          color: "#fff",
          fontSize: 14,
        },
        top: "0px",
        left: "0px",
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "15%",
        top: "15%",
        containLabel: true,
      },
      radar: {
        shape: "polygon",
        // 全局设置指标文字大小为14px
        name: {
          textStyle: {
            // color: '#333', origin
            color: "#fff",
            padding: [3, 5],
          },
        },
        indicator: [
          { name: "累计奖励", max: 100 },
          { name: "稳定性", max: 100 },
          { name: "收敛速度", max: 100 },
          { name: "复杂度", max: 100 },
          { name: "样本效率", max: 100 },
          { name: "鲁棒性", max: 100 },
        ],
        axisLine: {
          lineStyle: {
            // color: '#ddd' origin
            color: "#fff",
          },
        },
        splitLine: {
          lineStyle: {
            color: "#fff",
          },
        },
        splitArea: {
          show: true,
          areaStyle: {
            color: ["#f2f3f5", "#121724"],
          },
        },
      },
      textStyle: { fontSize: window.screen.width > 1536 ? "22" : "14" },
      series: [
        {
          name: "模型性能",
          type: "radar",
          areaStyle: {
            normal: {
              opacity: 0.5,
              color: "rgba(25, 183, 182, 0.4)", // 区域色
            },
          },
          lineStyle: {
            color: "rgb(25, 183, 182)", // 区域线条
          },
          data: [
            {
              value: [85, 90, 78, 88, 92, 65],
              name: "模型性能",
            },
          ],
        },
      ],
    };
    radarChartInstance.setOption(radarOption);
  }
};

const resizeCharts = () => {
  radarChartInstance?.resize();
  // 重置所有尺寸相关的缓存，因为窗口大小可能改变了容器宽度和字体大小
  resetContainerWidthCache();
  resetItemWidthCache();
};

const handleVisibilityChange = () => {
  console.log("Stop??????? Show???????????");
  if (document.hidden) {
    pauseMarquee();
  } else {
    resumeMarquee();
  }
};

const selectAlgorithm = (algo) => {
  currentAlgorithm.value = algo.id;
  console.log("用户选中算法:", algo);
  notify(`已选择算法: ${algo.name}`, "positive");
};

const startTraining = async () => {
  try {
    // 用户没选择算法，弹窗提示
    if (!currentAlgorithm.value) {
      notify("请先选择一个算法模型", "warning");
      return;
    }

    //当前选中的算法信息
    const selectedAlgo = algorithms.value.find(
      (algo) => algo.id === currentAlgorithm.value
    );
    if (!selectedAlgo) {
      notify("未找到选中的算法信息", "warning");
      return;
    }

    const timestamp = Date.now();
    const dateStr = new Date(timestamp)
      .toISOString()
      .slice(0, 19)
      .replace("T", "_")
      .replace(/:/g, "-");

    const trainingData = {
      name: `${selectedAlgo.name}_${dateStr}`,
      description: `基于${selectedAlgo.name}算法的深度学习训练任务`,
      training_type: "deep_learning",
      task_type: selectedAlgo.name,
      model_name: selectedAlgo.name,
    };

    console.log("创建训练任务>>>>>", trainingData);

    // 调用创建工作流接口
    const response = await createWorkFlow(trainingData);

    if (response && response.success) {
      // 重置之前的表单数据（避免新任务使用旧数据）
      modelFormStore.resetAllData();
      
      // 保存 task_id 到 store
      modelFormStore.setCurrentTaskId(response.task_id);

      // notify("训练任务创建成功!", "positive");

      router.push({
        path: "/ai-model/startTrain",
        query: {
          task_id: response.task_id,
          workflow_id: response.workflow_id,
          model_info_id: response.model_info_id,
          algorithm_name: selectedAlgo.name,
          algorithm_version: selectedAlgo.version,
        },
      });
    } else {
      notify({
        type: "negative",
        message: response?.message || "创建训练任务失败",
      });
    }
  } catch (error) {
    console.error("创建训练任务失败:", error);
    notify({
      type: "negative",
      message: "创建训练任务失败: " + (error.response?.data?.message || error.message),
    });
  }
};

onMounted(() => {
  fetchAlgorithms();
  fetchTaskList(); // 获取任务列表
  initCharts();
  nextTick(() => {
    startMarquee();
    window.addEventListener("resize", resizeCharts);
    document.addEventListener("visibilitychange", handleVisibilityChange);
  });
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", resizeCharts);
  document.removeEventListener("visibilitychange", handleVisibilityChange);
  stopMarquee();
  console.log("跑马灯销毁了 哈哈哈哈");

  if (radarChartInstance) {
    radarChartInstance.dispose();
    radarChartInstance = null;
  }

  resetContainerWidthCache();
  resetItemWidthCache();
});
</script>

<style lang="scss" scoped>
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .custom {
    /* 仅在Chrome中生效，设置padding-bottom为0 */
    padding-bottom: 0 !important;
  }
}
.custom{
  position: relative;
}
.doReturn{
  position: absolute;
  left: 0;
  top:-2vh;
}

.mt90 {
  margin-top: 1.125rem;
}

.q-page {
  background-color: #f5f7fa;
}

.algorithm-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  height: 100%;
  position: relative;
  width: 3.125rem;
  height: 1.875rem;

  .imgType {
    position: absolute;
    left: 0.125rem;
    top: 0.075rem;
    font-size: 0.15rem;
    color: $slideText;
    padding: 0.075rem;
    border: 0.0125rem solid #4ab4ff;
    border-radius: 0.0625rem;
    background: rgba(87, 88, 89, 0.6);
  }

  .imgName {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 0.55rem;
    line-height: 0.55rem;
    font-size: 0.15rem;
    text-align: center;
    background: rgba(0, 0, 0, 0.45);
    color: $slideText;
  }
}

.algorithm-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.selected-algorithm {
  border-color: var(--q-primary);
  box-shadow: 0 0 10px rgba(var(--q-primary-rgb), 0.3);
}

/* 确保文本不会溢出 */
.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.myCard {
  background: transparent !important;
  display: flex;
  border: none;
}

.showBorder {
  background: $tableBg;
  border: 1px solid transparent;
  border-image: linear-gradient(to bottom, #64778a, #293f64) 1;
}

.line {
  margin-top: 0.25rem;
  position: absolute;
  left: 0;
  top: 40px;
  width: 100%;
}

.cardContent {
  height: 80px;
  width: 100%;
  // padding: 10px 0px 0px 20px;
}

.mr {
  margin: 0 10px;
}

.mb {
  margin-bottom: 10px;
}

.leftTag {
  position: absolute;
  left: 10px;
  top: 6px;
  border: 1px solid #4083bc;
  border-radius: 5px;
  background: rgba(87, 88, 89, 0.5);
}

.title {
  font-size: 0.3rem;
  font-weight: 600;
}

.content {
  font-size: 0.225rem;
}

.font16 {
  font-size: 0.2rem;
}

.font14 {
  font-size: 0.175rem;
}

.quard-item {
  width: 3.125rem;
  height: 1.875rem;
  margin-right: 0.5rem;
}

.q-table__container {
  margin-top: 0.25rem;
  border: none !important;
  padding: 0 !important;
  min-height: 4.9rem;
  padding-left: 0.1875rem !important;
  padding-right: 0.1875rem !important;
  // padding-top:0 !important;
  // padding-bottom: .5rem !important;
  // box-shadow: 0 .0125rem .0625rem #0003, 0 .025rem .025rem #00000024, 0 .0375rem .0125rem -0.025rem #0000001f;
}

.mb10 {
  margin-bottom: 0.125rem;
}

.marquee-container {
  width: 100%;
  height: 24vh;
  margin: 0 auto;
  overflow: hidden;
  position: relative;
  background: $tableBg;
  margin: 0.125rem 0;
}

.marquee-content {
  display: flex;
  white-space: nowrap;
  width: max-content;
  padding: 0;
  list-style-type: none;
  margin-top: 0.35rem;
}

.marquee-item {
  display: inline-block;
  transition: transform 0.3s;
}

.marquee-item:hover {
  cursor: pointer;
  transform: scale(1.1);
}

.marquee-item.enlarged {
  z-index: 2;
}

.marquee-item.enlarged .algorithm-card {
  transform: scale(1.1);
  transition: transform 0.3s;
  box-shadow: 0 0 0.0625rem #fff;
}

.algotitle {
  padding-top: 0.2rem;
  padding-left: 0.2rem;
}

.botLine {
  position: relative;
  padding-left: 0.2rem;
  padding-right: 0.2rem;

  &::after {
    content: "";
    position: absolute;
    bottom: -0.125rem;
    left: 0;
    width: 100% !important;
    height: 0.0125rem;
    background: #2a3f63;
  }
}

.marquee-item .algorithm-card {
  width: 100%;
}

.cus-bg {
  border-radius: 0.0625rem;
  background: $primary;
  border: 0.0125rem solid transparent;
  border-radius: 0.0625rem;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image: linear-gradient(to right, $primary, $primary),
    linear-gradient(to right, #64778a, #293f64);
}

.cusCard {
  padding-top: 0.25rem;
}

.textSize {
  font-size: 0.3rem;
}

.radar {
  height: 5rem;
}

@media screen and (max-width: 1920px) {
  .radar {
    height: 5.6rem !important;
  }

  .evaluation-content {
    min-height: 5rem !important;
  }
}

/* 新布局样式 */
.evaluation-content {
  display: flex;
  align-items: center;
  min-height: 4.5rem;
}

.radar-chart-container {
  width: 100%;
  height: 4.5rem;
  min-height: 4.5rem;
}

/* 独立开始训练按钮样式 */
.training-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
}

.training-btn {
  width: 100%;
  min-height: 60px;
}

.training-btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 10px;
}

.entry-icon {
  width: 20px;
  height: 20px;
}

/* 训练任务卡片样式 */
.task-cards-container {
  height: 82vh;
  overflow-y: auto;
  padding: 0;
}

.task-card {
  box-shadow: 0 0.0625rem 0.375rem -0.0625rem rgba(0, 0, 0, 0.15);
  cursor: pointer;
  height: 12vh;
}

.task-card-inner {
  transition: all 0.3s ease;
}

.task-card-inner:hover {
  transform: translateY(-0.0625rem);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 16px;
}

/* 空状态样式 */
.empty-state {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 12px;
  background: none !important;
}

.empty-icon {
  color: #ccc;
  margin-bottom: 8px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-name {
  font-weight: 600;
  font-size: 0.2rem;
}

.task-status {
  display: flex;
  align-items: center;
}

.status-label {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  background: rgba(255, 255, 255, 0.1);
  font-size: 0.175rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  font-size: 0.175rem;
}

.info-label {
  color: rgba(255, 255, 255, 0.7);
}

.info-value {
  color: #fff;
  font-weight: 500;
}

.task-actions {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.status-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-label-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.175rem;
}

.status-value {
  font-weight: 500;
  font-size: 0.175rem;
}

/* 滚动条样式 */
.task-cards-container::-webkit-scrollbar {
  width: 6px;
}

.task-cards-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.task-cards-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.task-cards-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 加载更多和无更多数据提示样式 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.175rem;
}

.no-more-data {
  display: flex;
  justify-content: center;
  padding: 16px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.175rem;
}
</style>
