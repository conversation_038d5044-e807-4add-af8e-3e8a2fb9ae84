<!--
 * @Author: Szc
 * @Date: 2025-08-20 14:31:37
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-20 14:31:41
 * @Description: 
-->
<template>
  <div class="content">
    <div class="top">
      <div class="left">
        <div class="info-table">
          <div
            v-for="(row, index) in tableData"
            :key="index"
            class="table-row"
            :class="{ selected: selectedIndex === index }"
            @click="selectRow(index)"
          >
            <div class="cell-left">{{ row.label }}</div>
            <div class="cell-right">
              <span>{{ row.count }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="right">
        <div class="info-table">
          <div class="onetop">
            <img src="../../assets/images/icon_dz.png" />
            <h2>轨迹数据列表</h2>
          </div>
          <div class="twotiele">
            <div class="left-content">
              <p>
                共 {{ trajectoryData.length }} 条数据，已选择<span>{{
                  selected.length
                }}</span>
                条数据
              </p>
            </div>
            <div class="import-section" @click="importScenario">
              <div class="bts">
                <q-btn
                  class="import-btn roundBox"
                  color="primary"
                  label="一键修正"
                  @click="batchFix"
                >
                  <img src="../../assets/images/edit.png" class="btn-icon" />
                </q-btn>

                <q-btn class="save-btn roundBox" label="保存数据" @click="saveData">
                  <img
                    src="../../assets/reinforcementImages/icon_save.png"
                    class="btn-icon"
                  />
                </q-btn>
              </div>
            </div>
            <!-- <div class="right-content ">
                <q-btn class="amendment-btn roundBox" label="一键修正" @click="batchFix">
                  <img
                    src="../../assets/reinforcementImages/icon_drxd.png"
                    class="btn-icon"
                  />
                </q-btn>
                <q-btn class="save-btn roundBox" label="保存数据" @click="saveData">
                  <img
                    src="../../assets/reinforcementImages/icon_save.png"
                    class="btn-icon"
                  />
                </q-btn>
            </div> -->
          </div>
          <div class="three">
            <div class="q-table-container">
              <q-table
                :rows="pagedData"
                :columns="columns"
                row-key="ID"
                selection="multiple"
                v-model:selected="selected"
                :pagination="pagination.value"
                :loading="loading"
                :rows-per-page-options="[0]"
                hide-selection-column
              >
                <template #body-cell-ProblemTypes="{ row }">
                  <q-td class="text-center">
                    <q-badge v-if="row.ProblemTypes === '重复数据'" class="duplicate-data"
                      >重复数据</q-badge
                    >
                    <q-badge
                      v-else-if="row.ProblemTypes === '异常数据'"
                      class="abnormal-data"
                      >异常数据</q-badge
                    >
                    <q-badge
                      v-else-if="row.ProblemTypes === '缺失数据'"
                      class="missing-data"
                      >缺失数据</q-badge
                    >
                    <q-badge
                      v-else-if="row.ProblemTypes === '正常数据'"
                      class="normal-data"
                      >正常数据</q-badge
                    >
                  </q-td>
                </template>
                <template #body-cell-Status="{ row }">
                  <q-td class="text-center">
                    <div class="status-cell">
                      <img
                        v-if="row.Status === '待处理'"
                        src="../../assets/images/icon_pending.png"
                        class="status-photo"
                      />
                      <img
                        v-else
                        src="../../assets/images/icon_processed.png"
                        class="status-photo"
                      />
                      <q-badge v-if="row.Status === '待处理'" class="pending-status-badge"
                        >待处理</q-badge
                      >
                      <q-badge v-else class="processed-status-badge">已处理</q-badge>
                    </div>
                  </q-td>
                </template>
                <template #bottom>
                  <div class="row items-center justify-center full-width paginationEl">
                    <div style="margin-right: 20px">总计 {{ pagination.total }} 条</div>
                    <div>
                      <q-select
                        class="unsetHeight"
                        v-model="pagination.rowsPerPage"
                        :options="[5, 10]"
                        outlined
                        dense
                        options-dense
                        emit-value
                        map-options
                        @update:model-value="onRowsPerPageChange"
                      >
                        <template #selected> {{ pagination.rowsPerPage }}/页 </template>
                      </q-select>
                    </div>
                    <q-pagination
                      class="unsetHeight"
                      style="margin-left: 1rem; margin-right: 1rem"
                      v-model="pagination.page"
                      :max="Math.ceil(pagination.total / pagination.rowsPerPage)"
                      :max-pages="5"
                      boundary-numbers
                      direction-links
                    />
                    <div class="flexbox">
                      <div style="margin-right: 0.125rem">跳到</div>
                      <div class="roundBox">
                        <q-input
                          class="dynamic-label-input"
                          v-model="jumpText"
                          style="width: 0.625rem"
                          dense
                          @keyup.enter="goJump"
                        />
                      </div>
                      <q-btn class="custom-btn" label="跳转" @click="goJump" />
                    </div>
                  </div>
                </template>
              </q-table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="next">
      <q-btn class="prevBtn roundBox" color="grey-7" label="上一步" @click="prevStep" />
      <q-btn class="nextBtn roundBox" color="primary" label="下一步" @click="nextStep" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { usePlugin } from "composables/plugin.js";
const { notify } = usePlugin();
const emit = defineEmits(["next-step", "prev-step"]);
const nextStep = () => {
  emit("next-step");
};

const prevStep = () => {
  emit("prev-step");
};
const tableData = ref([
  { label: "样本总数量", count: 17500 },
  { label: "合格样本数量", count: 13497 },
  { label: "已处理样本数量", count: 399 },
  { label: "样本数据缺失", count: 218 },
  { label: "异常样本", count: 399 },
  { label: "重复样本", count: 218 },
]);
const trajectoryData = ref([
  {
    ID: "TRJ_0001",
    SampleContent: "轨迹点序列：[(116.025396,39.029872),...",
    vehicleType: "公交车",
    ProblemTypes: "重复数据",
    Status: "待处理",
  },
  {
    ID: "TRJ_0002",
    SampleContent: "轨迹点序列：[(116.025396,39.029872),...",
    vehicleType: "公交车",
    ProblemTypes: "异常数据",
    Status: "待处理",
  },
  {
    ID: "TRJ_0003",
    SampleContent: "轨迹点序列：[(116.025396,39.029872),...",
    vehicleType: "公交车",
    ProblemTypes: "正常数据",
    Status: "已处理",
  },
  {
    ID: "TRJ_0004",
    SampleContent: "轨迹点序列：[(116.025396,39.029872),...",
    vehicleType: "公交车",
    ProblemTypes: "缺失数据",
    Status: "已处理",
  },
  {
    ID: "TRJ_00015",
    SampleContent: "轨迹点序列：[(116.025396,39.029872),...",
    vehicleType: "公交车",
    ProblemTypes: "重复数据",
    Status: "待处理",
  },
  {
    ID: "TRJ_00025",
    SampleContent: "轨迹点序列：[(116.025396,39.029872),...",
    vehicleType: "公交车",
    ProblemTypes: "异常数据",
    Status: "待处理",
  },
  {
    ID: "TRJ_00035",
    SampleContent: "轨迹点序列：[(116.025396,39.029872),...",
    vehicleType: "公交车",
    ProblemTypes: "正常数据",
    Status: "已处理",
  },
  {
    ID: "TRJ_00045",
    SampleContent: "轨迹点序列：[(116.025396,39.029872),...",
    vehicleType: "公交车",
    ProblemTypes: "缺失数据",
    Status: "已处理",
  },
  {
    ID: "TRJ_00016",
    SampleContent: "轨迹点序列：[(116.025396,39.029872),...",
    vehicleType: "公交车",
    ProblemTypes: "重复数据",
    Status: "待处理",
  },
  {
    ID: "TRJ_00026",
    SampleContent: "轨迹点序列：[(116.025396,39.029872),...",
    vehicleType: "公交车",
    ProblemTypes: "异常数据",
    Status: "待处理",
  },
  {
    ID: "TRJ_00036",
    SampleContent: "轨迹点序列：[(116.025396,39.029872),...",
    vehicleType: "公交车",
    ProblemTypes: "正常数据",
    Status: "已处理",
  },
  {
    ID: "TRJ_00046",
    SampleContent: "轨迹点序列：[(116.025396,39.029872),...",
    vehicleType: "公交车",
    ProblemTypes: "缺失数据",
    Status: "已处理",
  },
  {
    ID: "TRJ_00017",
    SampleContent: "轨迹点序列：[(116.025396,39.029872),...",
    vehicleType: "公交车",
    ProblemTypes: "重复数据",
    Status: "待处理",
  },
  {
    ID: "TRJ_00027",
    SampleContent: "轨迹点序列：[(116.025396,39.029872),...",
    vehicleType: "公交车",
    ProblemTypes: "异常数据",
    Status: "待处理",
  },
  {
    ID: "TRJ_00037",
    SampleContent: "轨迹点序列：[(116.025396,39.029872),...",
    vehicleType: "公交车",
    ProblemTypes: "正常数据",
    Status: "已处理",
  },
  {
    ID: "TRJ_00047",
    SampleContent: "轨迹点序列：[(116.025396,39.029872),...",
    vehicleType: "公交车",
    ProblemTypes: "缺失数据",
    Status: "已处理",
  },
]);
const columns = ref([
  { name: "ID", label: "ID", field: "ID", align: "center", style: "width: 1fr" },
  {
    name: "SampleContent",
    label: "样本内容",
    field: "SampleContent",
    align: "center",
    style: "width: 2fr",
  },
  {
    name: "vehicleType",
    label: "车辆类型",
    field: "vehicleType",
    align: "center",
    style: "width: 1fr",
  },
  {
    name: "ProblemTypes",
    label: "问题类型",
    field: "ProblemTypes",
    align: "center",
    style: "width: 1fr",
  },
  {
    name: "Status",
    label: "状态",
    field: "Status",
    align: "center",
    style: "width: 1fr",
  },
]);
const selectedIndex = ref(0);
const selectRow = (index) => {
  selectedIndex.value = index;
};

const batchFix = () => {
  // 实现一键修正逻辑
  console.log("执行一键修正");
};

const saveData = () => {
  // 实现保存数据逻辑
  console.log("保存数据");
};
const loading = ref(false);
const selected = ref([]);
const pagination = ref({
  page: 1,
  rowsPerPage: 5,
  total: computed(() => trajectoryData.value.length),
});

// 添加分页后的数据计算属性
const pagedData = computed(() => {
  const startIndex = (pagination.value.page - 1) * pagination.value.rowsPerPage;
  const endIndex = startIndex + pagination.value.rowsPerPage;
  return trajectoryData.value.slice(startIndex, endIndex);
});

const onRowsPerPageChange = (val) => {
  pagination.value.rowsPerPage = val;
  pagination.value.page = 1; // 重置到第一页
};

const jumpText = ref("");
const goJump = () => {
  console.log("跳转到页面:", jumpText.value);
  const targetPage = Number(jumpText.value);
  if (
    targetPage > 0 &&
    targetPage <= Math.ceil(pagination.value.total / pagination.value.rowsPerPage)
  ) {
    pagination.value.page = targetPage;
    // fetchUsers()
  } else {
    notify("页码超出范围", "warning");
  }
};

// const goJump = () => {
//   const targetPage = Number(jumpText.value);
//   if (
//     !isNaN(targetPage) &&
//     targetPage > 0 &&
//     targetPage <= Math.ceil(pagination.value.total / pagination.value.rowsPerPage)
//   ) {
//     pagination.value.page = targetPage;
//     console.log("跳转到页面:", targetPage);
//   }
//   jumpText.value = "";
// };

// 添加watch监听分页变化
watch(
  () => pagination.value.page,
  () => {
    // 可以在这里添加分页变化时的逻辑，如重新获取数据等
    console.log("当前页码变化为:", pagination.value.page);
  }
);

watch(
  () => pagination.value.rowsPerPage,
  () => {
    // 当每页显示条数变化时的逻辑
    console.log("每页显示条数变化为:", pagination.value.rowsPerPage);
  }
);

const getStatusClass = (status) => {
  return status === "待处理" ? "pending-status" : "processed-status";
};

const getProblemTypeClass = (type) => {
  switch (type) {
    case "重复数据":
      return "duplicate-data";
    case "异常数据":
      return "abnormal-data";
    case "缺失数据":
      return "missing-data";
    case "正常数据":
      return "normal-data";
    default:
      return "";
  }
};
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%; // 确保根容器有高度
  position: relative;
  .next {
    top: -4vh;
    right: 0;
    position: absolute;
    display: flex;
    justify-content: space-between;
    gap: 0.25rem;
    width: 10%;

    .prevBtn {
      margin-right: auto;
    }

    .nextBtn {
      margin-left: auto;
    }
  }
  .top {
    display: flex;
    margin-bottom: 0.125rem;
    min-height: 9.5rem;
    flex: 1;

    .left {
      width: 15%;
      border: 0.025rem solid #707070;
      background-color: #181a24;
      padding: 0.125rem 0;
      display: flex;
      flex-direction: column;
      margin-right: 0.125rem;
      background-color: #181a24;
      background-image: repeating-linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.05) 0,
        rgba(255, 255, 255, 0.01) 0.05rem,
        transparent 0.0125rem,
        transparent 0.1875rem
      );

      .info-table {
        flex: 1;
        overflow-y: auto;

        .table-row {
          display: flex;
          align-items: center;
          justify-content: space-between;

          &.selected {
            background: rgba(16, 62, 77, 1);

            .cell-left {
              color: white;
            }

            .cell-right {
              span {
                background: rgba(206, 163, 69, 0.2);
                color: rgba(206, 163, 69, 1);
              }
            }
          }
        }

        .cell-left {
          flex: 2;
          padding: 0.125rem;
          color: #9cacc6;
          font-size: 0.2rem;
        }

        .cell-right {
          flex: 1;
          padding: 0.125rem;
          color: #ffffff;
          font-size: 0.2rem;
          text-align: right;
          span {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0.25rem;
            padding: 0 0.1rem;
          }
        }
      }
    }
  }

  .right {
    width: 85%;
    height: inherit;
    border: 0.025rem solid #707070;
    background-color: #181a24;
    padding: 0.25rem;
    display: flex;
    flex-direction: column;
    background-color: #181a24;
    background-image: repeating-linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.05) 0,
      rgba(255, 255, 255, 0.01) 0.05rem,
      transparent 0.0125rem,
      transparent 0.1875rem
    );

    .info-table {
      height: 100%;

      .onetop {
        display: flex;
        align-items: center;
        height: 5%;

        h2 {
          height: 0.1875rem;
          line-height: 0.1875rem;
          color: #ffffff;
          font-size: 0.225rem;
        }

        img {
          width: 0.2rem;
          height: 0.2rem;
          margin-right: 0.1rem;
        }
      }

      .twotiele {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 0.125rem;
        height: 5%;

        .left-content {
          display: flex;
          height: 0.5rem;
          line-height: 0.5rem;
          color: #ffffff;
          font-size: 0.175rem;

          span {
            margin-left: 0.05rem;
            color: rgba(255, 214, 127);
          }
        }

        .import-section {
          height: 0.5rem;

          .bts {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.125rem;

            .import-btn {
              width: 1.5rem;
              position: relative;
              padding: 0.1rem;
              color: #ffffff;

              .btn-icon {
                position: absolute;
                margin-left: 0.1rem;
                left: 0;
              }

              :deep(.q-btn__content) {
                margin-left: 0.3rem;
              }
            }

            .save-btn {
              width: 1.5rem;
              position: relative;
              padding: 0.1rem;
              background-color: rgba(255, 255, 255, 0.1);
              color: #ffffff;

              .btn-icon {
                position: absolute;
                margin-left: 0.1rem;
                left: 0;
              }

              :deep(.q-btn__content) {
                margin-left: 0.3rem;
              }
            }
          }
        }
      }

      .three {
        height: 80%;
        width: 100%;
        display: flex;
        flex-direction: column;

        .paginationEl {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .flexbox {
          display: flex;
          align-items: center;
        }
        .custom-btn {
          width: fit-content;
          background-color: #396ea4;
          margin-left: 0.875rem;
          border-radius: 0.0625rem;
        }
        :deep(.q-table__container) {
          background: none;
          border: none !important;
          height: 7.5rem !important;
          box-shadow: none !important;
        }

        :deep(.q-table__container .q-table thead th) {
          font-size: 0.175rem !important;
        }

        :deep(.q-table__container .q-table tbody td) {
          font-size: 0.175rem !important;
        }

        :deep(.q-table td) {
          padding: 0.0875rem 0.2rem !important;
        }

        :deep(.q-table tbody td) {
          height: 0.6rem !important;
        }

        :deep(.q-table th) {
          padding: 0.0875rem 0.2rem !important;
        }

        :deep(.q-table thead th) {
          height: 0.6rem !important;
        }

        .custom-btn {
          width: fit-content;
          background-color: #396ea4;
          margin-left: 0.875rem;
          border-radius: 0.0625rem;
        }

        :deep(.q-pagination .q-btn) {
          color: white !important;
        }

        :deep(.q-pagination .q-btn--standard) {
          background: #396ea4 !important;
        }

        :deep(.q-pagination .q-btn:hover) {
          background: #396ea4 !important;
        }

        .dynamic-label-input.q-field--with-content :deep(.q-field__label),
        .dynamic-label-input.q-field--focused :deep(.q-field__label) {
          display: none;
        }

        .dynamic-label-input :deep(.q-field__inner) {
          border: none;
        }

        .dynamic-label-input :deep(.q-field__inner) {
          border-radius: 0.1rem;
        }

        .dynamic-label-input :deep(.q-field__native) {
          text-align: center;
        }

        :deep(.q-table__bottom) {
          font-size: 0.185rem;
        }

        :deep(.q-btn__content) {
          font-size: 0.185rem;
        }

        :deep(.q-btn) {
          padding: 0.05rem 0.2rem;
          height: 0.45rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        :deep(.q-select) {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 0.185rem;
          height: 0.5rem;
        }

        :deep(.q-select__content) {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        :deep(.q-select__dropdown-icon) {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
        }

        :deep(.q-select__control) {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        :deep(.q-select__input) {
          text-align: center;
        }

        .dynamic-label-input :deep(.q-field__inner) {
          border: none;
          height: 0.5rem;
        }

        .dynamic-label-input :deep(.q-field__native) {
          text-align: center;
          font-size: 0.185rem;
        }

        :deep(.q-field--dense .q-field__control) {
          height: 0.5rem !important;
          align-items: center !important;
        }

        :deep(.q-field--dense .q-field__label) {
          font-size: 0.185rem !important;
        }

        :deep(.q-field__label) {
          line-height: 0.25rem !important;
          font-size: 0.185rem;
        }
        .status-cell {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.1rem;
          height: 100%;

          .status-photo {
            width: 0.3rem;
            height: 0.3rem;
            margin-left: auto;
            vertical-align: middle;
          }

          .pending-status-badge {
            margin-left: 0;
          }

          .processed-status-badge {
            margin-left: 0;
          }
        }

        .pending-status-badge {
          color: rgba(190, 141, 44, 1);
          background-color: transparent !important;
          border: none !important;
        }

        .processed-status-badge {
          color: rgba(37, 166, 27, 1);
          background-color: transparent !important;
          border: none !important;
        }

        .problem-type-cell {
          border: 0.025rem solid;
          border-radius: 0.05rem;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 0.75rem;
        }

        .duplicate-data {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 0.375rem;
          width: 0.85rem;
          border-radius: 0.375rem;
          background-color: rgba(78, 130, 176, 0.3);
          color: rgba(78, 130, 176, 1);
        }

        .abnormal-data {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 0.375rem;
          width: 0.85rem;
          border-radius: 0.375rem;
          background-color: rgba(192, 44, 44, 0.3);
          color: rgba(192, 44, 44, 1);
        }

        .missing-data {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 0.375rem;
          width: 0.85rem;
          border-radius: 0.375rem;
          background-color: rgba(255, 214, 127, 0.3);
          color: rgba(255, 214, 127, 1);
        }

        .normal-data {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 0.375rem;
          width: 0.85rem;
          border-radius: 0.375rem;
          background-color: rgba(57, 180, 29, 0.3);
          color: rgba(57, 180, 29, 1);
        }
      }
    }
  }

  .right {
    position: relative;

    &::before {
      position: absolute;
      z-index: 10000;
      content: "";
      left: -0.1rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198);
      border-radius: 0.125rem;
    }

    &::after {
      position: absolute;
      content: "";
      left: -0.075rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198, 0.5);
    }
  }

  .left {
    margin-right: 0.125rem;
    position: relative;

    &::before {
      position: absolute;
      z-index: 10000;
      content: "";
      left: -0.1rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198);
      border-radius: 0.125rem;
    }

    &::after {
      position: absolute;
      content: "";
      left: -0.075rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198, 0.5);
    }
  }
}
</style>
